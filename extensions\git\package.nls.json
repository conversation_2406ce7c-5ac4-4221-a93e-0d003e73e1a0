{"displayName": "Git", "description": "Git SCM Integration", "command.continueInLocalClone": "Clone Repository Locally and Open on Desktop...", "command.continueInLocalClone.qualifiedName": "Continue Working in New Local Clone", "command.clone": "<PERSON><PERSON>", "command.cloneRecursive": "<PERSON><PERSON> (Recursive)", "command.init": "Initialize Repository", "command.openRepository": "Open Repository", "command.reopenClosedRepositories": "Reopen Closed Repositories...", "command.close": "Close Repository", "command.closeOtherRepositories": "Close Other Repositories", "command.refresh": "Refresh", "command.openChange": "Open Changes", "command.openAllChanges": "Open All Changes", "command.openFile": "Open File", "command.openHEADFile": "Open File (HEAD)", "command.stage": "Stage Changes", "command.stageAll": "Stage All Changes", "command.stageAllTracked": "Stage All Tracked Changes", "command.stageAllUntracked": "Stage All Untracked Changes", "command.stageAllMerge": "Stage All Merge Changes", "command.stageSelectedRanges": "Stage Selected Ranges", "command.revertSelectedRanges": "Revert Selected Ranges", "command.stageChange": "Stage Change", "command.stageSelection": "Stage Selection", "command.stageBlock": "Stage Block", "command.revertChange": "Revert Change", "command.unstage": "Unstage Changes", "command.unstageAll": "Unstage All Changes", "command.unstageSelectedRanges": "Unstage Selected Ranges", "command.rename": "<PERSON><PERSON>", "command.clean": "Discard Changes", "command.cleanAll": "Discard All Changes", "command.cleanAllTracked": "Discard All Tracked Changes", "command.cleanAllUntracked": "Discard All Untracked Changes", "command.closeAllDiffEditors": "Close All Diff Editors", "command.closeAllUnmodifiedEditors": "Close All Unmodified Editors", "command.commit": "Commit", "command.commitAmend": "Commit (Amend)", "command.commitSigned": "Commit (Signed Off)", "command.commitStaged": "Commit Staged", "command.commitEmpty": "Commit Empty", "command.commitStagedSigned": "Commit Staged (Signed Off)", "command.commitStagedAmend": "Commit Staged (Amend)", "command.commitAll": "Commit All", "command.commitAllSigned": "Commit All (Signed Off)", "command.commitAllAmend": "Commit All (Amend)", "command.commitNoVerify": "Commit (No Verify)", "command.commitStagedNoVerify": "Commit Staged (No Verify)", "command.commitEmptyNoVerify": "Commit Empty (No Verify)", "command.commitStagedSignedNoVerify": "Commit Staged (Signed Off, No Verify)", "command.commitAmendNoVerify": "Commit (<PERSON>end, No Verify)", "command.commitSignedNoVerify": "Commit (Signed Off, No Verify)", "command.commitStagedAmendNoVerify": "Commit Staged (Amend, No Verify)", "command.commitAllNoVerify": "Commit All (No Verify)", "command.commitAllSignedNoVerify": "Commit All (Signed Off, No Verify)", "command.commitAllAmendNoVerify": "Commit All (<PERSON><PERSON>, No Verify)", "command.commitMessageAccept": "Accept Commit Message", "command.commitMessageDiscard": "Discard Commit Message", "command.restoreCommitTemplate": "Restore Commit Template", "command.undoCommit": "Undo Last Commit", "command.checkout": "Checkout to...", "command.checkoutDetached": "Checkout to (Detached)...", "command.branch": "Create Branch...", "command.branchFrom": "Create Branch From...", "command.deleteBranch": "Delete Branch...", "command.deleteRemoteBranch": "Delete Remote Branch...", "command.renameBranch": "Rename Branch...", "command.cherryPick": "Cherry Pick...", "command.cherryPickAbort": "Abort <PERSON>", "command.merge": "Merge...", "command.mergeAbort": "Abort <PERSON>", "command.rebase": "Rebase Branch...", "command.createTag": "Create Tag...", "command.deleteTag": "Delete Tag...", "command.deleteRemoteTag": "Delete Remote Tag...", "command.fetch": "<PERSON>tch", "command.fetchPrune": "<PERSON><PERSON> (<PERSON><PERSON>e)", "command.fetchAll": "<PERSON>tch From All Remotes", "command.pull": "<PERSON><PERSON>", "command.pullRebase": "Pull (Rebase)", "command.pullFrom": "Pull from...", "command.push": "<PERSON><PERSON>", "command.pushForce": "<PERSON><PERSON> (Force)", "command.pushTo": "Push to...", "command.pushToForce": "Push to... (Force)", "command.pushFollowTags": "Push (Follow Tags)", "command.pushFollowTagsForce": "Push (Follow Tags, Force)", "command.pushTags": "Push Tags", "command.addRemote": "Add Remote...", "command.removeRemote": "Remove Remote", "command.sync": "Sync", "command.syncRebase": "Sync (Rebase)", "command.publish": "Publish Branch...", "command.showOutput": "Show Git Output", "command.ignore": "Add to .gitignore", "command.revealInExplorer": "Reveal in Explorer View", "command.revealFileInOS.linux": "Open Containing Folder", "command.revealFileInOS.mac": "Reveal in Finder", "command.revealFileInOS.windows": "Reveal in File Explorer", "command.rebaseAbort": "Abort Rebase", "command.stashIncludeUntracked": "Stash (Include Untracked)", "command.stash": "Stash", "command.stashStaged": "Stash Staged", "command.stashPop": "Pop Stash...", "command.stashPopLatest": "Pop Latest Stash", "command.stashPopEditor": "Pop Stash", "command.stashApply": "Apply Stash...", "command.stashApplyLatest": "Apply Latest Stash", "command.stashApplyEditor": "Apply Stash", "command.stashDrop": "Drop Stash...", "command.stashDropAll": "Drop All Stashes...", "command.stashDropEditor": "Drop Stash", "command.stashView": "View Stash...", "command.timelineOpenDiff": "Open Changes", "command.timelineCopyCommitId": "Copy Commit ID", "command.timelineCopyCommitMessage": "Copy Commit Message", "command.timelineSelectForCompare": "Select for Compare", "command.timelineCompareWithSelected": "Compare with Selected", "command.manageUnsafeRepositories": "Manage Unsafe Repositories", "command.openRepositoriesInParentFolders": "Open Repositories In Parent Folders", "command.viewChanges": "Open Changes", "command.viewStagedChanges": "Open Staged Changes", "command.viewUntrackedChanges": "Open Untracked Changes", "command.viewCommit": "Open Commit", "command.graphCheckout": "Checkout", "command.graphCheckoutDetached": "Checkout (Detached)", "command.graphCherryPick": "<PERSON> Pick", "command.graphDeleteBranch": "Delete Branch", "command.graphDeleteTag": "Delete Tag", "command.blameToggleEditorDecoration": "Toggle Git Blame Editor Decoration", "command.blameToggleStatusBarItem": "Toggle Git Blame Status Bar Item", "command.api.getRepositories": "Get Repositories", "command.api.getRepositoryState": "Get Repository State", "command.api.getRemoteSources": "Get Remote Sources", "command.git.acceptMerge": "Complete Merge", "command.git.openMergeEditor": "Resolve in Merge Editor", "command.git.runGitMerge": "Compute Conflicts With Git", "command.git.runGitMergeDiff3": "Compute Conflicts With Git (Diff3)", "config.enabled": "Whether Git is enabled.", "config.path": "Path and filename of the git executable, e.g. `C:\\Program Files\\Git\\bin\\git.exe` (Windows). This can also be an array of string values containing multiple paths to look up.", "config.autoRepositoryDetection": "Configures when repositories should be automatically detected.", "config.autoRepositoryDetection.true": "Scan for both subfolders of the current opened folder and parent folders of open files.", "config.autoRepositoryDetection.false": "Disable automatic repository scanning.", "config.autoRepositoryDetection.subFolders": "Scan for subfolders of the currently opened folder.", "config.autoRepositoryDetection.openEditors": "Scan for parent folders of open files.", "config.autorefresh": "Whether auto refreshing is enabled.", "config.autofetch": "When set to true, commits will automatically be fetched from the default remote of the current Git repository. Setting to `all` will fetch from all remotes.", "config.autofetchPeriod": "Duration in seconds between each automatic git fetch, when `#git.autofetch#` is enabled.", "config.confirmSync": "Confirm before synchronizing Git repositories.", "config.countBadge": "Controls the Git count badge.", "config.countBadge.all": "Count all changes.", "config.countBadge.tracked": "Count only tracked changes.", "config.countBadge.off": "Turn off counter.", "config.checkoutType": "Controls what type of Git refs are listed when running `Checkout to...`.", "config.checkoutType.local": "Local branches", "config.checkoutType.tags": "Tags", "config.checkoutType.remote": "Remote branches", "config.defaultBranchName": "The name of the default branch (example: main, trunk, development) when initializing a new Git repository. When set to empty, the default branch name configured in Git will be used. **Note:** Requires Git version `2.28.0` or later.", "config.branchPrefix": "Prefix used when creating a new branch.", "config.branchProtection": "List of protected branches. By default, a prompt is shown before changes are committed to a protected branch. The prompt can be controlled using the `#git.branchProtectionPrompt#`  setting.", "config.branchProtectionPrompt": "Controls whether a prompt is being shown before changes are committed to a protected branch.", "config.branchProtectionPrompt.alwaysCommit": "Always commit changes to the protected branch.", "config.branchProtectionPrompt.alwaysCommitToNewBranch": "Always commit changes to a new branch.", "config.branchProtectionPrompt.alwaysPrompt": "Always prompt before changes are committed to a protected branch.", "config.branchRandomNameDictionary": "List of dictionaries used for the randomly generated branch name. Each value represents the dictionary used to generate the segment of the branch name. Supported dictionaries: `adjectives`, `animals`, `colors` and `numbers`.", "config.branchRandomNameDictionary.adjectives": "A random adjective", "config.branchRandomNameDictionary.animals": "A random animal name", "config.branchRandomNameDictionary.colors": "A random color name", "config.branchRandomNameDictionary.numbers": "A random number between 100 and 999", "config.branchRandomNameEnable": "Controls whether a random name is generated when creating a new branch.", "config.branchValidationRegex": "A regular expression to validate new branch names.", "config.branchWhitespaceChar": "The character to replace whitespace in new branch names, and to separate segments of a randomly generated branch name.", "config.ignoreLegacyWarning": "Ignores the legacy Git warning.", "config.ignoreMissingGitWarning": "Ignores the warning when <PERSON><PERSON> is missing.", "config.ignoreWindowsGit27Warning": "Ignores the warning when Git 2.25 - 2.26 is installed on Windows.", "config.ignoreLimitWarning": "Ignores the warning when there are too many changes in a repository.", "config.ignoreRebaseWarning": "Ignores the warning when it looks like the branch might have been rebased when pulling.", "config.defaultCloneDirectory": "The default location to clone a Git repository.", "config.useEditorAsCommitInput": "Controls whether a full text editor will be used to author commit messages, whenever no message is provided in the commit input box.", "config.verboseCommit": "Enable verbose output when `#git.useEditorAsCommitInput#` is enabled.", "config.enableSmartCommit": "Commit all changes when there are no staged changes.", "config.smartCommitChanges": "Control which changes are automatically staged by Smart Commit.", "config.smartCommitChanges.all": "Automatically stage all changes.", "config.smartCommitChanges.tracked": "Automatically stage tracked changes only.", "config.suggestSmartCommit": "Suggests to enable smart commit (commit all changes when there are no staged changes).", "config.enableCommitSigning": "Enables commit signing with GPG, X.509, or SSH.", "config.discardAllScope": "Controls what changes are discarded by the `Discard all changes` command. `all` discards all changes. `tracked` discards only tracked files. `prompt` shows a prompt dialog every time the action is run.", "config.decorations.enabled": "Controls whether Git contributes colors and badges to the Explorer and the Open Editors view.", "config.enableStatusBarSync": "Controls whether the Git Sync command appears in the status bar.", "config.followTagsWhenSync": "Push all annotated tags when running the sync command.", "config.replaceTagsWhenPull": "Automatically replace the local tags with the remote tags in case of a conflict when running the pull command.", "config.promptToSaveFilesBeforeStash": "Controls whether Git should check for unsaved files before stashing changes.", "config.promptToSaveFilesBeforeStash.always": "Check for any unsaved files.", "config.promptToSaveFilesBeforeStash.staged": "Check only for unsaved staged files.", "config.promptToSaveFilesBeforeStash.never": "Disable this check.", "config.promptToSaveFilesBeforeCommit": "Controls whether Git should check for unsaved files before committing.", "config.promptToSaveFilesBeforeCommit.always": "Check for any unsaved files.", "config.promptToSaveFilesBeforeCommit.staged": "Check only for unsaved staged files.", "config.promptToSaveFilesBeforeCommit.never": "Disable this check.", "config.postCommitCommand": "Run a git command after a successful commit.", "config.postCommitCommand.none": "Don't run any command after a commit.", "config.postCommitCommand.push": "Run 'git push' after a successful commit.", "config.postCommitCommand.sync": "Run 'git pull' and 'git push' after a successful commit.", "config.rememberPostCommitCommand": "Remember the last git command that ran after a commit.", "config.openAfterClone": "Controls whether to open a repository automatically after cloning.", "config.openAfterClone.always": "Always open in current window.", "config.openAfterClone.alwaysNewWindow": "Always open in a new window.", "config.openAfterClone.whenNoFolderOpen": "Only open in current window when no folder is opened.", "config.openAfterClone.prompt": "Always prompt for action.", "config.showInlineOpenFileAction": "Controls whether to show an inline Open File action in the Git changes view.", "config.showPushSuccessNotification": "Controls whether to show a notification when a push is successful.", "config.inputValidation": "Controls whether to show commit message input validation diagnostics.", "config.inputValidationLength": "Controls the commit message length threshold for showing a warning.", "config.inputValidationSubjectLength": "Controls the commit message subject length threshold for showing a warning. Unset it to inherit the value of `#git.inputValidationLength#`.", "config.detectSubmodules": "Controls whether to automatically detect Git submodules.", "config.detectSubmodulesLimit": "Controls the limit of Git submodules detected.", "config.alwaysShowStagedChangesResourceGroup": "Always show the Staged Changes resource group.", "config.alwaysSignOff": "Controls the signoff flag for all commits.", "config.ignoreSubmodules": "Ignore modifications to submodules in the file tree.", "config.ignoredRepositories": "List of Git repositories to ignore.", "config.scanRepositories": "List of paths to search for Git repositories in.", "config.commandsToLog": {"message": "List of git commands (ex: commit, push) that would have their `stdout` logged to the [git output](command:git.showOutput). If the git command has a client-side hook configured, the client-side hook's `stdout` will also be logged to the [git output](command:git.showOutput).", "comment": ["{Locked='](command:git.showOutput'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "config.showProgress": "Controls whether Git actions should show progress.", "config.rebaseWhenSync": "Force Git to use rebase when running the sync command.", "config.confirmEmptyCommits": "Always confirm the creation of empty commits for the 'Git: Commit Empty' command.", "config.fetchOnPull": "When enabled, fetch all branches when pulling. Otherwise, fetch just the current one.", "config.pullBeforeCheckout": "Controls whether a branch that does not have outgoing commits is fast-forwarded before it is checked out.", "config.pullTags": "Fetch all tags when pulling.", "config.pruneOnFetch": "Prune when fetching.", "config.autoStash": "Stash any changes before pulling and restore them after successful pull.", "config.allowForcePush": "Controls whether force push (with or without lease) is enabled.", "config.useForcePushWithLease": "Controls whether force pushing uses the safer force-with-lease variant.", "config.useForcePushIfIncludes": "Controls whether force pushing uses the safer force-if-includes variant. Note: This setting requires the `#git.useForcePushWithLease#` setting to be enabled, and Git version `2.30.0` or later.", "config.confirmForcePush": "Controls whether to ask for confirmation before force-pushing.", "config.allowNoVerifyCommit": "Controls whether commits without running pre-commit and commit-msg hooks are allowed.", "config.confirmNoVerifyCommit": "Controls whether to ask for confirmation before committing without verification.", "config.closeDiffOnOperation": "Controls whether the diff editor should be automatically closed when changes are stashed, committed, discarded, staged, or unstaged.", "config.openDiffOnClick": "Controls whether the diff editor should be opened when clicking a change. Otherwise the regular editor will be opened.", "config.supportCancellation": "Controls whether a notification comes up when running the Sync action, which allows the user to cancel the operation.", "config.branchSortOrder": "Controls the sort order for branches.", "config.untrackedChanges": "Controls how untracked changes behave.", "config.untrackedChanges.mixed": "All changes, tracked and untracked, appear together and behave equally.", "config.untrackedChanges.separate": "Untracked changes appear separately in the Source Control view. They are also excluded from several actions.", "config.untrackedChanges.hidden": "Untracked changes are hidden and excluded from several actions.", "config.requireGitUserConfig": "Controls whether to require explicit Git user configuration or allow Git to guess if missing.", "config.showCommitInput": "Controls whether to show the commit input in the Git source control panel.", "config.terminalAuthentication": "Controls whether to enable VS Code to be the authentication handler for Git processes spawned in the Integrated Terminal. Note: Terminals need to be restarted to pick up a change in this setting.", "config.terminalGitEditor": "Controls whether to enable VS Code to be the Git editor for Git processes spawned in the integrated terminal. Note: Terminals need to be restarted to pick up a change in this setting.", "config.timeline.showAuthor": "Controls whether to show the commit author in the Timeline view.", "config.timeline.showUncommitted": "Controls whether to show uncommitted changes in the Timeline view.", "config.timeline.date": "Controls which date to use for items in the Timeline view.", "config.timeline.date.committed": "Use the committed date", "config.timeline.date.authored": "Use the authored date", "config.useCommitInputAsStashMessage": "Controls whether to use the message from the commit input box as the default stash message.", "config.showActionButton": "Controls whether an action button is shown in the Source Control view.", "config.showActionButton.commit": "Show an action button to commit changes when the local branch has modified files ready to be committed.", "config.showActionButton.publish": "Show an action button to publish the local branch when it does not have a tracking remote branch.", "config.showActionButton.sync": "Show an action button to synchronize changes when the local branch is either ahead or behind the remote branch.", "config.statusLimit": "Controls how to limit the number of changes that can be parsed from Git status command. Can be set to 0 for no limit.", "config.experimental.installGuide": "Experimental improvements for the Git setup flow.", "config.repositoryScanIgnoredFolders": "List of folders that are ignored while scanning for Git repositories when `#git.autoRepositoryDetection#` is set to `true` or `subFolders`.", "config.repositoryScanMaxDepth": "Controls the depth used when scanning workspace folders for Git repositories when `#git.autoRepositoryDetection#` is set to `true` or `subFolders`. Can be set to `-1` for no limit.", "config.useIntegratedAskPass": "Controls whether GIT_ASKPASS should be overwritten to use the integrated version.", "config.mergeEditor": "Open the merge editor for files that are currently under conflict.", "config.optimisticUpdate": "Controls whether to optimistically update the state of the Source Control view after running git commands.", "config.openRepositoryInParentFolders": "Control whether a repository in parent folders of workspaces or open files should be opened.", "config.openRepositoryInParentFolders.always": "Always open a repository in parent folders of workspaces or open files.", "config.openRepositoryInParentFolders.never": "Never open a repository in parent folders of workspaces or open files.", "config.openRepositoryInParentFolders.prompt": "Prompt before opening a repository the parent folders of workspaces or open files.", "config.publishBeforeContinueOn": "Controls whether to publish unpublished Git state when using Continue Working On from a Git repository.", "config.publishBeforeContinueOn.always": "Always publish unpublished Git state when using Continue Working On from a Git repository", "config.publishBeforeContinueOn.never": "Never publish unpublished Git state when using Continue Working On from a Git repository", "config.publishBeforeContinueOn.prompt": "Prompt to publish unpublished Git state when using Continue Working On from a Git repository", "config.similarityThreshold": "Controls the threshold of the similarity index (the amount of additions/deletions compared to the file's size) for changes in a pair of added/deleted files to be considered a rename. **Note:** Requires Git version `2.18.0` or later.", "config.blameEditorDecoration.enabled": "Controls whether to show blame information in the editor using editor decorations.", "config.blameEditorDecoration.template": "Template for the blame information editor decoration. Supported variables:\n\n* `hash`: Commit hash\n\n* `hashShort`: First N characters of the commit hash according to `#git.commitShortHashLength#`\n\n* `subject`: First line of the commit message\n\n* `authorName`: Author name\n\n* `authorEmail`: Author email\n\n* `authorDate`: Author date\n\n* `authorDateAgo`: Time difference between now and the author date\n\n", "config.blameStatusBarItem.enabled": "Controls whether to show blame information in the status bar.", "config.blameStatusBarItem.template": "Template for the blame information status bar item. Supported variables:\n\n* `hash`: Commit hash\n\n* `hashShort`: First N characters of the commit hash according to `#git.commitShortHashLength#`\n\n* `subject`: First line of the commit message\n\n* `authorName`: Author name\n\n* `authorEmail`: Author email\n\n* `authorDate`: Author date\n\n* `authorDateAgo`: Time difference between now and the author date\n\n", "config.commitShortHashLength": "Controls the length of the commit short hash.", "config.diagnosticsCommitHook.Enabled": "Controls whether to check for unresolved diagnostics before committing.", "config.diagnosticsCommitHook.Sources": "Controls the list of sources (**Item**) and the minimum severity (**Value**) to be considered before committing. **Note:** To ignore diagnostics from a particular source, add the source to the list and set the minimum severity to `none`.", "config.discardUntrackedChangesToTrash": "Controls whether discarding untracked changes moves the file(s) to the Recycle Bin (Windows), Trash (macOS, Linux) instead of deleting them permanently. **Note:** This setting has no effect when connected to a remote or when running in Linux as a snap package.", "config.showReferenceDetails": "Controls whether to show the details of the last commit for Git refs in the checkout, branch, and tag pickers.", "submenu.explorer": "Git", "submenu.commit": "Commit", "submenu.commit.amend": "Amend", "submenu.commit.signoff": "Sign Off", "submenu.changes": "Changes", "submenu.pullpush": "<PERSON>ull, Push", "submenu.branch": "Branch", "submenu.remotes": "Remote", "submenu.stash": "Stash", "submenu.tags": "Tags", "colors.added": "Color for added resources.", "colors.modified": "Color for modified resources.", "colors.stageModified": "Color for modified resources which have been staged.", "colors.stageDeleted": "Color for deleted resources which have been staged.", "colors.deleted": "Color for deleted resources.", "colors.renamed": "Color for renamed or copied resources.", "colors.untracked": "Color for untracked resources.", "colors.ignored": "Color for ignored resources.", "colors.conflict": "Color for resources with conflicts.", "colors.submodule": "Color for submodule resources.", "colors.incomingAdded": "Color for added incoming resource.", "colors.incomingDeleted": "Color for deleted incoming resource.", "colors.incomingRenamed": "Color for renamed incoming resource.", "colors.incomingModified": "Color for modified incoming resource.", "colors.blameEditorDecoration": "Color for the blame editor decoration.", "view.workbench.scm.missing.windows": {"message": "[Download Git for Windows](https://git-scm.com/download/win)\nAfter installing, please [reload](command:workbench.action.reloadWindow) (or [troubleshoot](command:git.showOutput)). Additional source control providers can be installed [from the Marketplace](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22).", "comment": ["{Locked='](command:workbench.action.reloadWindow'}", "{Locked='](command:git.showOutput'}", "{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.missing.mac": {"message": "[Download Git for macOS](https://git-scm.com/download/mac)\nAfter installing, please [reload](command:workbench.action.reloadWindow) (or [troubleshoot](command:git.showOutput)). Additional source control providers can be installed [from the Marketplace](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22).", "comment": ["{Locked='](command:workbench.action.reloadWindow'}", "{Locked='](command:git.showOutput'}", "{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.missing.linux": {"message": "Source control depends on Git being installed.\n[Download Git for Linux](https://git-scm.com/download/linux)\nAfter installing, please [reload](command:workbench.action.reloadWindow) (or [troubleshoot](command:git.showOutput)). Additional source control providers can be installed [from the Marketplace](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22).", "comment": ["{Locked='](command:workbench.action.reloadWindow'}", "{Locked='](command:git.showOutput'}", "{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.missing": {"message": "Install Git, a popular source control system, to track code changes and collaborate with others. Learn more in our [Git guides](https://aka.ms/vscode-scm).", "comment": ["{Locked='](https://aka.ms/vscode-scm'}", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.disabled": {"message": "If you would like to use Git features, please enable Git in your [settings](command:workbench.action.openSettings?%5B%22git.enabled%22%5D).\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:workbench.action.openSettings?%5B%22git.enabled%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.empty": {"message": "In order to use Git features, you can open a folder containing a Git repository or clone from a URL.\n[Open Folder](command:vscode.openFolder)\n[Clone Repository](command:git.cloneRecursive)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:vscode.openFolder'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.folder": {"message": "The folder currently open doesn't have a Git repository. You can initialize a repository which will enable source control features powered by Git.\n[Initialize Repository](command:git.init?%5Btrue%5D)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:git.init?%5Btrue%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.workspace": {"message": "The workspace currently open doesn't have any folders containing Git repositories. You can initialize a repository on a folder which will enable source control features powered by Git.\n[Initialize Repository](command:git.init)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:git.init'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.emptyWorkspace": {"message": "The workspace currently open doesn't have any folders containing Git repositories.\n[Add Folder to Workspace](command:workbench.action.addRootFolder)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:workbench.action.addRootFolder'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.scanFolderForRepositories": {"message": "Scanning folder for Git repositories..."}, "view.workbench.scm.scanWorkspaceForRepositories": {"message": "Scanning workspace for Git repositories..."}, "view.workbench.scm.repositoryInParentFolders": {"message": "A Git repository was found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether Git repositories in parent folders of workspaces or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders).", "comment": ["{Locked='](command:git.openRepositoriesInParentFolders'}", "{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.repositoriesInParentFolders": {"message": "Git repositories were found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether Git repositories in parent folders of workspace or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders).", "comment": ["{Locked='](command:git.openRepositoriesInParentFolders'}", "{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.unsafeRepository": {"message": "The detected Git repository is potentially unsafe as the folder is owned by someone other than the current user.\n[Manage Unsafe Repositories](command:git.manageUnsafeRepositories)\nTo learn more about unsafe repositories [read our docs](https://aka.ms/vscode-git-unsafe-repository).", "comment": ["{Locked='](command:git.manageUnsafeRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.unsafeRepositories": {"message": "The detected Git repositories are potentially unsafe as the folders are owned by someone other than the current user.\n[Manage Unsafe Repositories](command:git.manageUnsafeRepositories)\nTo learn more about unsafe repositories [read our docs](https://aka.ms/vscode-git-unsafe-repository).", "comment": ["{Locked='](command:git.manageUnsafeRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.closedRepository": {"message": "A Git repository was found that was previously closed.\n[Reopen Closed Repository](command:git.reopenClosedRepositories)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:git.reopenClosedRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.closedRepositories": {"message": "Git repositories were found that were previously closed.\n[Reopen Closed Repositories](command:git.reopenClosedRepositories)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).", "comment": ["{Locked='](command:git.reopenClosedRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.cloneRepository": {"message": "You can clone a repository locally.\n[Clone Repository](command:git.clone 'Clone a repository once the Git extension has activated')", "comment": ["{Locked='](command:git.clone'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.learnMore": "To learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm)."}