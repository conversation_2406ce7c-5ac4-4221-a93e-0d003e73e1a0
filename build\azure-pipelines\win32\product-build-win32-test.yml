parameters:
  - name: VSCODE_QUALITY
    type: string
  - name: VSCODE_ARCH
    type: string
  - name: VSCODE_RUN_UNIT_TESTS
    type: boolean
  - name: VSCODE_RUN_INTEGRATION_TESTS
    type: boolean
  - name: VSCODE_RUN_SMOKE_TESTS
    type: boolean
  - name: PUBLISH_TASK_NAME
    type: string
    default: PublishPipelineArtifact@0

steps:
  - powershell: npm exec -- npm-run-all -lp "electron $(VSCODE_ARCH)" "playwright-install"
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Download Electron and Playwright
    retryCountOnTaskFailure: 3

  - ${{ if eq(parameters.VSCODE_RUN_UNIT_TESTS, true) }}:
    - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: .\scripts\test.bat --tfs "Unit Tests"
        displayName: Run unit tests (Electron)
        timeoutInMinutes: 15
      - powershell: npm run test-node
        displayName: Run unit tests (node.js)
        timeoutInMinutes: 15
      - powershell: node test/unit/browser/index.js --browser chromium --tfs "Browser Unit Tests"
        displayName: Run unit tests (Browser, Chromium)
        timeoutInMinutes: 20

    - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: .\scripts\test.bat --build --tfs "Unit Tests"
        displayName: Run unit tests (Electron)
        timeoutInMinutes: 15
      - powershell: npm run test-node -- --build
        displayName: Run unit tests (node.js)
        timeoutInMinutes: 15
      - powershell: npm run test-browser-no-install -- --build --browser chromium --tfs "Browser Unit Tests"
        displayName: Run unit tests (Browser, Chromium)
        timeoutInMinutes: 20

  - ${{ if eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, true) }}:
    - powershell: |
        . build/azure-pipelines/win32/exec.ps1
        $ErrorActionPreference = "Stop"
        exec { npm run gulp `
          compile-extension:configuration-editing `
          compile-extension:css-language-features-server `
          compile-extension:emmet `
          compile-extension:git `
          compile-extension:github-authentication `
          compile-extension:html-language-features-server `
          compile-extension:ipynb `
          compile-extension:notebook-renderers `
          compile-extension:json-language-features-server `
          compile-extension:markdown-language-features `
          compile-extension-media `
          compile-extension:microsoft-authentication `
          compile-extension:typescript-language-features `
          compile-extension:vscode-api-tests `
          compile-extension:vscode-colorize-tests `
          compile-extension:vscode-colorize-perf-tests `
          compile-extension:vscode-test-resolver `
        }
      displayName: Build integration tests

    - powershell: .\build\azure-pipelines\win32\listprocesses.bat
      displayName: Diagnostics before integration test runs
      continueOnError: true
      condition: succeededOrFailed()

    - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: .\scripts\test-integration.bat --tfs "Integration Tests"
        displayName: Run integration tests (Electron)
        timeoutInMinutes: 20

      - powershell: .\scripts\test-web-integration.bat --browser chromium
        displayName: Run integration tests (Browser, Chromium)
        timeoutInMinutes: 20

      - powershell: .\scripts\test-remote-integration.bat
        displayName: Run integration tests (Remote)
        timeoutInMinutes: 20

    - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: |
          # Figure out the full absolute path of the product we just built
          # including the remote server and configure the integration tests
          # to run with these builds instead of running out of sources.
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          $AppRoot = "$(agent.builddirectory)\VSCode-win32-$(VSCODE_ARCH)"
          $AppProductJson = Get-Content -Raw -Path "$AppRoot\resources\app\product.json" | ConvertFrom-Json
          $AppNameShort = $AppProductJson.nameShort
          $env:INTEGRATION_TEST_ELECTRON_PATH = "$AppRoot\$AppNameShort.exe"
          $env:VSCODE_REMOTE_SERVER_PATH = "$(agent.builddirectory)\vscode-server-win32-$(VSCODE_ARCH)"
          exec { .\scripts\test-integration.bat --build --tfs "Integration Tests" }
        displayName: Run integration tests (Electron)
        timeoutInMinutes: 20

      - powershell: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          $env:VSCODE_REMOTE_SERVER_PATH = "$(agent.builddirectory)\vscode-server-win32-$(VSCODE_ARCH)-web"
          exec { .\scripts\test-web-integration.bat --browser firefox }
        displayName: Run integration tests (Browser, Firefox)
        timeoutInMinutes: 20

      - powershell: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          $AppRoot = "$(agent.builddirectory)\VSCode-win32-$(VSCODE_ARCH)"
          $AppProductJson = Get-Content -Raw -Path "$AppRoot\resources\app\product.json" | ConvertFrom-Json
          $AppNameShort = $AppProductJson.nameShort
          $env:INTEGRATION_TEST_ELECTRON_PATH = "$AppRoot\$AppNameShort.exe"
          $env:VSCODE_REMOTE_SERVER_PATH = "$(agent.builddirectory)\vscode-server-win32-$(VSCODE_ARCH)"
          exec { .\scripts\test-remote-integration.bat }
        displayName: Run integration tests (Remote)
        timeoutInMinutes: 20

      - powershell: .\build\azure-pipelines\win32\listprocesses.bat
        displayName: Diagnostics after integration test runs
        continueOnError: true
        condition: succeededOrFailed()

  - ${{ if eq(parameters.VSCODE_RUN_SMOKE_TESTS, true) }}:
    - powershell: .\build\azure-pipelines\win32\listprocesses.bat
      displayName: Diagnostics before smoke test run
      continueOnError: true
      condition: succeededOrFailed()

    - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: npm run compile
        workingDirectory: test/smoke
        displayName: Compile smoke tests

      - powershell: npm run gulp compile-extension-media
        displayName: Build extensions for smoke tests

      - powershell: npm run smoketest-no-compile -- --tracing
        displayName: Run smoke tests (Electron)
        timeoutInMinutes: 20

    - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: npm run smoketest-no-compile -- --tracing --build "$(agent.builddirectory)\VSCode-win32-$(VSCODE_ARCH)"
        displayName: Run smoke tests (Electron)
        timeoutInMinutes: 20

      - powershell: npm run smoketest-no-compile -- --web --tracing --headless
        env:
          VSCODE_REMOTE_SERVER_PATH: $(agent.builddirectory)\vscode-server-win32-$(VSCODE_ARCH)-web
        displayName: Run smoke tests (Browser, Chromium)
        timeoutInMinutes: 20

      - powershell: npm run gulp compile-extension:vscode-test-resolver
        displayName: Compile test resolver extension
        timeoutInMinutes: 20

      - powershell: npm run smoketest-no-compile -- --tracing --remote --build "$(agent.builddirectory)\VSCode-win32-$(VSCODE_ARCH)"
        env:
          VSCODE_REMOTE_SERVER_PATH: $(agent.builddirectory)\vscode-server-win32-$(VSCODE_ARCH)
        displayName: Run smoke tests (Remote)
        timeoutInMinutes: 20

    - powershell: .\build\azure-pipelines\win32\listprocesses.bat
      displayName: Diagnostics after smoke test run
      continueOnError: true
      condition: succeededOrFailed()

  - ${{ if or(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, true), eq(parameters.VSCODE_RUN_SMOKE_TESTS, true)) }}:
    - task: ${{ parameters.PUBLISH_TASK_NAME }}
      inputs:
        targetPath: .build\crashes
        ${{ if and(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, true), eq(parameters.VSCODE_RUN_SMOKE_TESTS, false)) }}:
          artifactName: crash-dump-windows-$(VSCODE_ARCH)-integration-$(System.JobAttempt)
        ${{ elseif and(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, false), eq(parameters.VSCODE_RUN_SMOKE_TESTS, true)) }}:
          artifactName: crash-dump-windows-$(VSCODE_ARCH)-smoke-$(System.JobAttempt)
        ${{ else }}:
          artifactName: crash-dump-windows-$(VSCODE_ARCH)-$(System.JobAttempt)
        sbomEnabled: false
      displayName: "Publish Crash Reports"
      continueOnError: true
      condition: failed()

    # In order to properly symbolify above crash reports
    # (if any), we need the compiled native modules too
    - task: ${{ parameters.PUBLISH_TASK_NAME }}
      inputs:
        targetPath: node_modules
        ${{ if and(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, true), eq(parameters.VSCODE_RUN_SMOKE_TESTS, false)) }}:
          artifactName: node-modules-windows-$(VSCODE_ARCH)-integration-$(System.JobAttempt)
        ${{ elseif and(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, false), eq(parameters.VSCODE_RUN_SMOKE_TESTS, true)) }}:
          artifactName: node-modules-windows-$(VSCODE_ARCH)-smoke-$(System.JobAttempt)
        ${{ else }}:
          artifactName: node-modules-windows-$(VSCODE_ARCH)-$(System.JobAttempt)
        sbomEnabled: false
      displayName: "Publish Node Modules"
      continueOnError: true
      condition: failed()

    - task: ${{ parameters.PUBLISH_TASK_NAME }}
      inputs:
        targetPath: .build\logs
        ${{ if and(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, true), eq(parameters.VSCODE_RUN_SMOKE_TESTS, false)) }}:
          artifactName: logs-windows-$(VSCODE_ARCH)-integration-$(System.JobAttempt)
        ${{ elseif and(eq(parameters.VSCODE_RUN_INTEGRATION_TESTS, false), eq(parameters.VSCODE_RUN_SMOKE_TESTS, true)) }}:
          artifactName: logs-windows-$(VSCODE_ARCH)-smoke-$(System.JobAttempt)
        ${{ else }}:
          artifactName: logs-windows-$(VSCODE_ARCH)-$(System.JobAttempt)
        sbomEnabled: false
      displayName: "Publish Log Files"
      continueOnError: true
      condition: succeededOrFailed()

  - task: PublishTestResults@2
    displayName: Publish Tests Results
    inputs:
      testResultsFiles: "*-results.xml"
      searchFolder: "$(Build.ArtifactStagingDirectory)/test-results"
    condition: succeededOrFailed()
