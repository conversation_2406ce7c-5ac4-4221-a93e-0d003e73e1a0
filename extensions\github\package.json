{"name": "github", "displayName": "%displayName%", "description": "%description%", "publisher": "vscode", "license": "MIT", "version": "0.0.1", "engines": {"vscode": "^1.41.0"}, "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "icon": "images/icon.png", "categories": ["Other"], "activationEvents": ["*"], "extensionDependencies": ["vscode.git-base"], "main": "./out/extension.js", "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": true}}, "enabledApiProposals": ["canonicalUri<PERSON>rovider", "contribEditSessions", "contribShareMenu", "contribSourceControlHistoryItemMenu", "scm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shareProvider", "timeline"], "contributes": {"commands": [{"command": "github.publish", "title": "%command.publish%"}, {"command": "github.copyVscodeDevLink", "title": "%command.copyVscodeDevLink%"}, {"command": "github.copyVscodeDevLinkFile", "title": "%command.copyVscodeDevLink%"}, {"command": "github.copyVscodeDevLinkWithoutRange", "title": "%command.copyVscodeDevLink%"}, {"command": "github.openOnVscodeDev", "title": "%command.openOnVscodeDev%", "icon": "$(globe)"}, {"command": "github.graph.openOnGitHub", "title": "%command.openOnGitHub%", "icon": "$(github)"}, {"command": "github.timeline.openOnGitHub", "title": "%command.openOnGitHub%", "icon": "$(github)"}], "continueEditSession": [{"command": "github.openOnVscodeDev", "when": "github.hasGitHubRepo", "qualifiedName": "Continue Working in vscode.dev", "category": "Remote Repositories", "remoteGroup": "virtualfs_44_vscode-vfs_2_web@2"}], "menus": {"commandPalette": [{"command": "github.publish", "when": "git-base.gitEnabled && workspaceFolderCount != 0 && remoteName != 'codespaces'"}, {"command": "github.graph.openOnGitHub", "when": "false"}, {"command": "github.copyVscodeDevLink", "when": "false"}, {"command": "github.copyVscodeDevLinkFile", "when": "false"}, {"command": "github.copyVscodeDevLinkWithoutRange", "when": "false"}, {"command": "github.openOnVscodeDev", "when": "false"}, {"command": "github.timeline.openOnGitHub", "when": "false"}], "file/share": [{"command": "github.copyVscodeDevLinkFile", "when": "github.hasGitHubRepo && remoteName != 'codespaces'", "group": "0_vscode@0"}], "editor/context/share": [{"command": "github.copyVscodeDevLink", "when": "github.hasGitHubRepo && resourceScheme != untitled && !isInEmbeddedEditor && remoteName != 'codespaces'", "group": "0_vscode@0"}], "explorer/context/share": [{"command": "github.copyVscodeDevLinkWithoutRange", "when": "github.hasGitHubRepo && resourceScheme != untitled && !isInEmbeddedEditor && remoteName != 'codespaces'", "group": "0_vscode@0"}], "editor/lineNumber/context": [{"command": "github.copyVscodeDevLink", "when": "github.hasGitHubRepo && resourceScheme != untitled && activeEditor == workbench.editors.files.textFileEditor && config.editor.lineNumbers == on && remoteName != 'codespaces'", "group": "1_cutcopypaste@2"}, {"command": "github.copyVscodeDevLink", "when": "github.hasGitHubRepo && resourceScheme != untitled && activeEditor == workbench.editor.notebook && remoteName != 'codespaces'", "group": "1_cutcopypaste@2"}], "editor/title/context/share": [{"command": "github.copyVscodeDevLinkWithoutRange", "when": "github.hasGitHubRepo && resourceScheme != untitled && remoteName != 'codespaces'", "group": "0_vscode@0"}], "scm/historyItem/context": [{"command": "github.graph.openOnGitHub", "when": "github.hasGitHubRepo", "group": "0_view@2"}], "scm/historyItem/hover": [{"command": "github.graph.openOnGitHub", "when": "github.hasGitHubRepo", "group": "1_open@1"}], "timeline/item/context": [{"command": "github.timeline.openOnGitHub", "group": "1_actions@3", "when": "github.hasGitHubRepo && timelineItem =~ /git:file:commit\\b/"}]}, "configuration": [{"title": "GitHub", "properties": {"github.branchProtection": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.branchProtection%"}, "github.gitAuthentication": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.gitAuthentication%"}, "github.gitProtocol": {"type": "string", "enum": ["https", "ssh"], "default": "https", "description": "%config.gitProtocol%"}, "github.showAvatar": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.showAvatar%"}}}], "viewsWelcome": [{"view": "scm", "contents": "%welcome.publishFolder%", "when": "config.git.enabled && git.state == initialized && workbenchState == folder && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0"}, {"view": "scm", "contents": "%welcome.publishWorkspaceFolder%", "when": "config.git.enabled && git.state == initialized && workbenchState == workspace && workspaceFolderCount != 0 && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0"}], "markdown.previewStyles": ["./markdown.css"]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "gulp compile-extension:github", "watch": "gulp watch-extension:github"}, "dependencies": {"@octokit/graphql": "8.2.0", "@octokit/graphql-schema": "14.4.0", "@octokit/rest": "21.1.0", "tunnel": "^0.0.6", "@vscode/extension-telemetry": "^0.9.8"}, "devDependencies": {"@types/node": "20.x"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}