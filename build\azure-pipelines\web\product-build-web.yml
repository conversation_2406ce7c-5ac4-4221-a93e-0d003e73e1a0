steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - template: ../distro/download-distro.yml@self

  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get Secrets"
    inputs:
      azureSubscription: vscode
      KeyVaultName: vscode-build-secrets
      SecretsFilter: "github-distro-mixin-password"

  - task: DownloadPipelineArtifact@2
    inputs:
      artifact: Compilation
      path: $(Build.ArtifactStagingDirectory)
    displayName: Download compilation output

  - script: tar -xzf $(Build.ArtifactStagingDirectory)/compilation.tar.gz
    displayName: Extract compilation output

  - script: node build/setup-npm-registry.js $NPM_REGISTRY
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Registry

  - script: mkdir -p .build && node build/azure-pipelines/common/computeNodeModulesCacheKey.js web > .build/packagelockhash
    displayName: Prepare node_modules cache key

  - task: Cache@2
    inputs:
      key: '"node_modules" | .build/packagelockhash'
      path: .build/node_modules_cache
      cacheHitVar: NODE_MODULES_RESTORED
    displayName: Restore node_modules cache

  - script: tar -xzf .build/node_modules_cache/cache.tgz
    condition: and(succeeded(), eq(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Extract node_modules cache

  - script: |
      set -e
      # Set the private NPM registry to the global npmrc file
      # so that authentication works for subfolders like build/, remote/, extensions/ etc
      # which does not have their own .npmrc file
      npm config set registry "$NPM_REGISTRY"
      echo "##vso[task.setvariable variable=NPMRC_PATH]$(npm config get userconfig)"
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM

  - task: npmAuthenticate@0
    inputs:
      workingFile: $(NPMRC_PATH)
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Authentication

  - script: |
      set -e
      ./build/azure-pipelines/linux/apt-retry.sh sudo apt-get update
      ./build/azure-pipelines/linux/apt-retry.sh sudo apt-get install -y libkrb5-dev
    displayName: Setup system services
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))

  - script: |
      set -e

      for i in {1..5}; do # try 5 times
        npm ci && break
        if [ $i -eq 5 ]; then
          echo "Npm install failed too many times" >&2
          exit 1
        fi
        echo "Npm install failed $i, trying again..."
      done
    env:
      ELECTRON_SKIP_BINARY_DOWNLOAD: 1
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Install dependencies
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))

  - script: node build/azure-pipelines/distro/mixin-npm
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Mixin distro node modules

  - script: |
      set -e
      node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
      mkdir -p .build/node_modules_cache
      tar -czf .build/node_modules_cache/cache.tgz --files-from .build/node_modules_list.txt
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Create node_modules archive

  - script: node build/azure-pipelines/distro/mixin-quality
    displayName: Mixin distro quality

  - template: ../common/install-builtin-extensions.yml@self

  - script: |
      set -e
      npm run gulp vscode-web-min-ci
      ARCHIVE_PATH=".build/web/vscode-web.tar.gz"
      mkdir -p $(dirname $ARCHIVE_PATH)
      tar --owner=0 --group=0 -czf $ARCHIVE_PATH -C .. vscode-web
      echo "##vso[task.setvariable variable=WEB_PATH]$ARCHIVE_PATH"
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Build

  - task: AzureCLI@2
    displayName: Fetch secrets from Azure
    inputs:
      azureSubscription: vscode
      scriptType: pscore
      scriptLocation: inlineScript
      addSpnToEnvironment: true
      inlineScript: |
        Write-Host "##vso[task.setvariable variable=AZURE_TENANT_ID]$env:tenantId"
        Write-Host "##vso[task.setvariable variable=AZURE_CLIENT_ID]$env:servicePrincipalId"
        Write-Host "##vso[task.setvariable variable=AZURE_ID_TOKEN;issecret=true]$env:idToken"

  - script: |
      set -e
      AZURE_STORAGE_ACCOUNT="vscodeweb" \
      AZURE_TENANT_ID="$(AZURE_TENANT_ID)" \
      AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
      AZURE_ID_TOKEN="$(AZURE_ID_TOKEN)" \
        node build/azure-pipelines/upload-cdn
    displayName: Upload to CDN

  - script: |
      set -e
      AZURE_STORAGE_ACCOUNT="vscodeweb" \
      AZURE_TENANT_ID="$(AZURE_TENANT_ID)" \
      AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
      AZURE_ID_TOKEN="$(AZURE_ID_TOKEN)" \
        node build/azure-pipelines/upload-sourcemaps out-vscode-web-min out-vscode-web-min/vs/workbench/workbench.web.main.js.map
    displayName: Upload sourcemaps (Web Main)

  - script: |
      set -e
      AZURE_STORAGE_ACCOUNT="vscodeweb" \
      AZURE_TENANT_ID="$(AZURE_TENANT_ID)" \
      AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
      AZURE_ID_TOKEN="$(AZURE_ID_TOKEN)" \
        node build/azure-pipelines/upload-sourcemaps out-vscode-web-min out-vscode-web-min/vs/workbench/workbench.web.main.internal.js.map
    displayName: Upload sourcemaps (Web Internal)

  - script: |
      set -e
      AZURE_STORAGE_ACCOUNT="vscodeweb" \
      AZURE_TENANT_ID="$(AZURE_TENANT_ID)" \
      AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" \
      AZURE_ID_TOKEN="$(AZURE_ID_TOKEN)" \
        node build/azure-pipelines/upload-nlsmetadata
    displayName: Upload NLS Metadata

  - script: echo "##vso[task.setvariable variable=ARTIFACT_PREFIX]attempt$(System.JobAttempt)_"
    condition: and(succeededOrFailed(), notIn(variables['Agent.JobStatus'], 'Succeeded', 'SucceededWithIssues'))
    displayName: Generate artifact prefix

  - task: 1ES.PublishPipelineArtifact@1
    inputs:
      targetPath: $(WEB_PATH)
      artifactName: $(ARTIFACT_PREFIX)vscode_web_linux_standalone_archive-unsigned
      sbomBuildDropPath: $(Agent.BuildDirectory)/vscode-web
      sbomPackageName: "VS Code Web"
      sbomPackageVersion: $(Build.SourceVersion)
    condition: and(succeededOrFailed(), ne(variables['WEB_PATH'], ''))
    displayName: Publish web archive
