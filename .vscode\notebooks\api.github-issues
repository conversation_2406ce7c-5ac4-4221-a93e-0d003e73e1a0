[{"kind": 1, "language": "markdown", "value": "#### Config"}, {"kind": 2, "language": "github-issues", "value": "$REPO=repo:microsoft/vscode\n$MILESTONE=milestone:\"March 2025\""}, {"kind": 1, "language": "markdown", "value": "### Finalization"}, {"kind": 2, "language": "github-issues", "value": "$REPO $MILESTONE label:api-finalization"}, {"kind": 1, "language": "markdown", "value": "### Proposals"}, {"kind": 2, "language": "github-issues", "value": "$REPO $MILESTONE is:open label:api-proposal sort:created-asc"}]