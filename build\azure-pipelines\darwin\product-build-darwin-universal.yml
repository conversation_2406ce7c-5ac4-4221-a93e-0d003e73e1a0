steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - template: ../distro/download-distro.yml@self

  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get Secrets"
    inputs:
      azureSubscription: vscode
      KeyVaultName: vscode-build-secrets
      SecretsFilter: "github-distro-mixin-password,macos-developer-certificate,macos-developer-certificate-key"

  - script: node build/setup-npm-registry.js $NPM_REGISTRY build
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Registry

  - script: |
      set -e
      # Set the private NPM registry to the global npmrc file
      # so that authentication works for subfolders like build/, remote/, extensions/ etc
      # which does not have their own .npmrc file
      npm config set registry "$NPM_REGISTRY"
      echo "##vso[task.setvariable variable=NPMRC_PATH]$(npm config get userconfig)"
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM

  - task: npmAuthenticate@0
    inputs:
      workingFile: $(NPMRC_PATH)
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Authentication

  - script: |
      set -e

      for i in {1..5}; do # try 5 times
        npm ci && break
        if [ $i -eq 5 ]; then
          echo "Npm install failed too many times" >&2
          exit 1
        fi
        echo "Npm install failed $i, trying again..."
      done
    workingDirectory: build
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Install build dependencies

  - download: current
    artifact: unsigned_vscode_client_darwin_x64_archive
    displayName: Download x64 artifact

  - download: current
    artifact: unsigned_vscode_client_darwin_arm64_archive
    displayName: Download arm64 artifact

  - script: node build/azure-pipelines/distro/mixin-quality
    displayName: Mixin distro quality

  - script: |
      set -e
      unzip $(Pipeline.Workspace)/unsigned_vscode_client_darwin_x64_archive/VSCode-darwin-x64.zip -d $(agent.builddirectory)/VSCode-darwin-x64
      unzip $(Pipeline.Workspace)/unsigned_vscode_client_darwin_arm64_archive/VSCode-darwin-arm64.zip -d $(agent.builddirectory)/VSCode-darwin-arm64
      DEBUG=* node build/darwin/create-universal-app.js $(agent.builddirectory)
    displayName: Create Universal App

  - script: |
      set -e
      APP_ROOT="$(Agent.BuildDirectory)/VSCode-darwin-$(VSCODE_ARCH)"
      APP_NAME="`ls $APP_ROOT | head -n 1`"
      APP_PATH="$APP_ROOT/$APP_NAME" node build/darwin/verify-macho.js universal
    displayName: Verify arch of Mach-O objects

  - script: |
      set -e
      security create-keychain -p pwd $(agent.tempdirectory)/buildagent.keychain
      security default-keychain -s $(agent.tempdirectory)/buildagent.keychain
      security unlock-keychain -p pwd $(agent.tempdirectory)/buildagent.keychain
      echo "$(macos-developer-certificate)" | base64 -D > $(agent.tempdirectory)/cert.p12
      security import $(agent.tempdirectory)/cert.p12 -k $(agent.tempdirectory)/buildagent.keychain -P "$(macos-developer-certificate-key)" -T /usr/bin/codesign
      export CODESIGN_IDENTITY=$(security find-identity -v -p codesigning $(agent.tempdirectory)/buildagent.keychain | grep -oEi "([0-9A-F]{40})" | head -n 1)
      security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k pwd $(agent.tempdirectory)/buildagent.keychain
      DEBUG=electron-osx-sign* node build/darwin/sign.js $(agent.builddirectory)
    displayName: Set Hardened Entitlements

  - script: pushd $(agent.builddirectory)/VSCode-darwin-$(VSCODE_ARCH) && zip -r -X -y $(agent.builddirectory)/VSCode-darwin-$(VSCODE_ARCH).zip * && popd
    displayName: Archive build

  - task: 1ES.PublishPipelineArtifact@1
    inputs:
      targetPath: $(Agent.BuildDirectory)/VSCode-darwin-$(VSCODE_ARCH).zip
      artifactName: unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive
      sbomBuildDropPath: $(Agent.BuildDirectory)/VSCode-darwin-$(VSCODE_ARCH)
      sbomPackageName: "VS Code macOS $(VSCODE_ARCH) (unsigned)"
      sbomPackageVersion: $(Build.SourceVersion)
    displayName: Publish client archive
