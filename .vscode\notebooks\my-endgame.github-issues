[{"kind": 1, "language": "markdown", "value": "#### <PERSON>ros"}, {"kind": 2, "language": "github-issues", "value": "$REPOS=repo:microsoft/lsprotocol repo:microsoft/monaco-editor repo:microsoft/vscode repo:microsoft/vscode-anycode repo:microsoft/vscode-autopep8 repo:microsoft/vscode-black-formatter repo:microsoft/vscode-copilot repo:microsoft/vscode-copilot-release repo:microsoft/vscode-dev repo:microsoft/vscode-dev-chrome-launcher repo:microsoft/vscode-emmet-helper repo:microsoft/vscode-extension-telemetry repo:microsoft/vscode-flake8 repo:microsoft/vscode-github-issue-notebooks repo:microsoft/vscode-hexeditor repo:microsoft/vscode-internalbacklog repo:microsoft/vscode-isort repo:microsoft/vscode-js-debug repo:microsoft/vscode-jupyter repo:microsoft/vscode-jupyter-internal repo:microsoft/vscode-l10n repo:microsoft/vscode-livepreview repo:microsoft/vscode-markdown-languageservice repo:microsoft/vscode-markdown-tm-grammar repo:microsoft/vscode-mypy repo:microsoft/vscode-pull-request-github repo:microsoft/vscode-pylint repo:microsoft/vscode-python repo:microsoft/vscode-python-debugger repo:microsoft/vscode-python-tools-extension-template repo:microsoft/vscode-references-view repo:microsoft/vscode-remote-release repo:microsoft/vscode-remote-repositories-github repo:microsoft/vscode-remote-tunnels repo:microsoft/vscode-remotehub repo:microsoft/vscode-settings-sync-server repo:microsoft/vscode-unpkg repo:microsoft/vscode-vsce\n\n$MILESTONE=milestone:\"March 2025\"\n\n$MINE=assignee:@me"}, {"kind": 1, "language": "markdown", "value": "# Preparation"}, {"kind": 1, "language": "markdown", "value": "## Open Pull Requests on the Milestone"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:pr is:open"}, {"kind": 1, "language": "markdown", "value": "## Open Issues on the Milestone"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue is:open -label:iteration-plan -label:endgame-plan -label:testplan-item"}, {"kind": 1, "language": "markdown", "value": "## Feature Requests Missing Labels"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue is:closed reason:completed label:feature-request -label:verification-needed -label:on-testplan -label:verified -label:*duplicate"}, {"kind": 1, "language": "markdown", "value": "## Test Plan Items"}, {"kind": 2, "language": "github-issues", "value": "$REPOS is:issue is:open author:@me label:testplan-item"}, {"kind": 1, "language": "markdown", "value": "## Verification Needed"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue is:closed reason:completed label:feature-request label:verification-needed -label:verified -label:on-testplan"}, {"kind": 1, "language": "markdown", "value": "# Testing"}, {"kind": 1, "language": "markdown", "value": "## Test Plan Items"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MINE is:issue is:open label:testplan-item"}, {"kind": 1, "language": "markdown", "value": "## Verification Needed"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE -$MINE is:issue is:closed reason:completed -assignee:@me -label:verified -label:z-author-verified label:feature-request label:verification-needed -label:verification-steps-needed -label:unreleased -label:on-testplan"}, {"kind": 1, "language": "markdown", "value": "# Fixing"}, {"kind": 1, "language": "markdown", "value": "## Open Issues"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue is:open -label:endgame-plan -label:testplan-item -label:iteration-plan"}, {"kind": 1, "language": "markdown", "value": "## Open Bugs"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue is:open label:bug"}, {"kind": 1, "language": "markdown", "value": "# Verification"}, {"kind": 1, "language": "markdown", "value": "## My Issues (verification-steps-needed)"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue label:bug label:verification-steps-needed"}, {"kind": 1, "language": "markdown", "value": "## My Issues (verification-found)"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue label:bug label:verification-found"}, {"kind": 1, "language": "markdown", "value": "## Issues filed by me"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE -$MINE is:issue is:closed reason:completed author:@me sort:updated-asc label:bug -label:unreleased -label:verified -label:z-author-verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:error-telemetry -label:verification-steps-needed -label:triage-needed -label:verification-found -label:*not-reproducible"}, {"kind": 1, "language": "markdown", "value": "## Issues filed from outside team"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE -$MINE is:issue is:closed reason:completed sort:updated-asc label:bug -label:unreleased -label:verified -label:z-author-verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:*out-of-scope -label:error-telemetry -label:verification-steps-needed -label:verification-found -author:aeschli -author:alexdima -author:alexr00 -author:<PERSON><PERSON><PERSON><PERSON> -author:andreamah -author:bamurtaugh -author:bpasero -author:chris<PERSON>s -author:chrmarti -author:Chuxel -author:claudiaregio -author:connor4312 -author:dbaeumer -author:deepak1556 -author:devinvalenciano -author:digitarald -author:<PERSON><PERSON><PERSON><PERSON><PERSON> -author:egamma -author:fiveisprime -author:ntrogh -author:hediet -author:isidorn -author:joaomoreno -author:joyce<PERSON>hl -author:jrieken -author:kieferrm -author:lramos15 -author:lszomoru -author:meganrogge -author:misolori -author:mjbvz -author:rebornix -author:roblourens -author:rzhao271 -author:sandy081 -author:sbatten -author:stevencl -author:tanhakabir -author:<PERSON><PERSON><PERSON><PERSON> -author:Tyriar -author:weinand -author:amunger -author:karthiknadig -author:eleanorjboyd -author:Yoyokrazy -author:paulacamargo25 -author:ulugbekna -author:aiday-mar -author:daviddossett -author:bhavyaus -author:justschen -author:benibenj -author:luabud -author:anthonykim1 -author:joshspicer -author:osortega -author:legomushroom"}, {"kind": 1, "language": "markdown", "value": "## Issues filed by others"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE -$MINE is:issue is:closed reason:completed -author:@me sort:updated-asc label:bug -label:unreleased -label:verified -label:z-author-verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:error-telemetry -label:verification-steps-needed -label:verification-found -label:*not-reproducible -label:*out-of-scope"}, {"kind": 1, "language": "markdown", "value": "## Test steps needed from others"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE -$MINE is:issue label:bug label:verification-steps-needed -label:verified"}, {"kind": 1, "language": "markdown", "value": "# Release Notes"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE $MINE is:issue is:closed reason:completed label:feature-request -label:on-release-notes\r\n$REPOS $MILESTONE $MINE is:issue is:closed reason:completed label:engineering -label:on-release-notes\r\n$REPOS $MILESTONE $MINE is:issue is:closed reason:completed label:plan-item -label:on-release-notes"}]