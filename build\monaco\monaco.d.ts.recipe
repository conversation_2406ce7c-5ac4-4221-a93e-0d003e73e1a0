/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

declare let MonacoEnvironment: monaco.Environment | undefined;

interface Window {
	MonacoEnvironment?: monaco.Environment | undefined;
}

declare namespace monaco {

	export type Thenable<T> = PromiseLike<T>;

	export interface Environment {
		/**
		 * Define a global `monaco` symbol.
		 * This is true by default in AMD and false by default in ESM.
		 */
		globalAPI?: boolean;
		/**
		 * The base url where the editor sources are found (which contains the vs folder)
		 */
		baseUrl?: string;
		/**
		 * A web worker factory.
		 * NOTE: If `getWorker` is defined, `getWorkerUrl` is not invoked.
		 */
		getWorker?(workerId: string, label: string): Promise<Worker> | Worker;
		/**
		 * Return the location for web worker scripts.
		 * NOTE: If `getWorker` is defined, `getWorkerUrl` is not invoked.
		 */
		getWorkerUrl?(workerId: string, label: string): string;
		/**
		 * Create a trusted types policy (same API as window.trustedTypes.createPolicy)
		 */
		createTrustedTypesPolicy?(
			policyName: string,
			policyOptions?: ITrustedTypePolicyOptions,
		): undefined | ITrustedTypePolicy;
	}

	export interface ITrustedTypePolicyOptions {
		createHTML?: (input: string, ...arguments: any[]) => string;
		createScript?: (input: string, ...arguments: any[]) => string;
		createScriptURL?: (input: string, ...arguments: any[]) => string;
	}

	export interface ITrustedTypePolicy {
		readonly name: string;
		createHTML?(input: string): any;
		createScript?(input: string): any;
		createScriptURL?(input: string): any;
	}

	export interface IDisposable {
		dispose(): void;
	}

	export interface IEvent<T> {
		(listener: (e: T) => any, thisArg?: any): IDisposable;
	}

	/**
	 * A helper that allows to emit and listen to typed events
	 */
	export class Emitter<T> {
		constructor();
		readonly event: Event<T>;
		fire(event: T): void;
		dispose(): void;
	}

#include(vs/platform/markers/common/markers): MarkerTag, MarkerSeverity
#include(vs/base/common/cancellation): CancellationTokenSource, CancellationToken
#include(vs/base/common/uri): URI, UriComponents
#include(vs/base/common/keyCodes): KeyCode
#include(vs/editor/common/services/editorBaseApi): KeyMod
#include(vs/base/common/htmlContent): IMarkdownString, MarkdownStringTrustedOptions
#include(vs/base/browser/keyboardEvent): IKeyboardEvent
#include(vs/base/browser/mouseEvent): IMouseEvent
#include(vs/editor/common/editorCommon): IScrollEvent
#include(vs/editor/common/core/position): IPosition, Position
#include(vs/editor/common/core/range): IRange, Range
#include(vs/editor/common/core/selection): ISelection, Selection, SelectionDirection
#include(vs/editor/common/languages): Token
}

declare namespace monaco.editor {
#includeAll(vs/editor/standalone/browser/standaloneEditor;languages.Token=>Token):
#include(vs/editor/standalone/common/standaloneTheme): BuiltinTheme, IStandaloneThemeData, IColors
#include(vs/editor/common/languages/supports/tokenization): ITokenThemeRule
#include(vs/editor/standalone/browser/standaloneWebWorker): MonacoWebWorker, IInternalWebWorkerOptions
#include(vs/editor/standalone/browser/standaloneCodeEditor): IActionDescriptor, IGlobalEditorOptions, IStandaloneEditorConstructionOptions, IStandaloneDiffEditorConstructionOptions, IStandaloneCodeEditor, IStandaloneDiffEditor
export interface ICommandHandler {
	(...args: any[]): void;
}
export interface ILocalizedString {
	original: string;
	value: string;
}
export interface ICommandMetadata {
	readonly description: ILocalizedString | string;
}
#include(vs/platform/contextkey/common/contextkey): IContextKey, ContextKeyValue
#include(vs/editor/standalone/browser/standaloneServices): IEditorOverrideServices
#include(vs/platform/markers/common/markers): IMarker, IMarkerData, IRelatedInformation
#include(vs/editor/standalone/browser/colorizer): IColorizerOptions, IColorizerElementOptions
#include(vs/base/common/scrollable): ScrollbarVisibility
#include(vs/base/common/themables): ThemeColor, ThemeIcon
#include(vs/editor/common/core/editOperation): ISingleEditOperation
#include(vs/editor/common/core/wordHelper): IWordAtPosition
#includeAll(vs/editor/common/model): IScrollEvent
#include(vs/editor/common/diff/legacyLinesDiffComputer): IChange, ICharChange, ILineChange
#include(vs/editor/common/core/dimension): IDimension
#includeAll(vs/editor/common/editorCommon): IScrollEvent
#includeAll(vs/editor/common/textModelEvents):
#includeAll(vs/editor/common/cursorEvents):
#include(vs/platform/accessibility/common/accessibility): AccessibilitySupport
#includeAll(vs/editor/common/config/editorOptions):
#include(vs/editor/browser/config/editorConfiguration): IEditorConstructionOptions
#includeAll(vs/editor/browser/editorBrowser;editorCommon.=>):
#include(vs/editor/common/config/fontInfo): FontInfo, BareFontInfo
#include(vs/editor/common/config/editorZoom): EditorZoom, IEditorZoom

//compatibility:
export type IReadOnlyModel = ITextModel;
export type IModel = ITextModel;
}

declare namespace monaco.languages {

#include(vs/base/common/glob): IRelativePattern
#include(vs/editor/common/languageSelector): LanguageSelector, LanguageFilter
#includeAll(vs/editor/standalone/browser/standaloneLanguages;languages.=>;editorCommon.=>editor.;model.=>editor.;IMarkerData=>editor.IMarkerData):
#includeAll(vs/editor/common/languages/languageConfiguration):
#includeAll(vs/editor/common/languages;IMarkerData=>editor.IMarkerData;ISingleEditOperation=>editor.ISingleEditOperation;model.=>editor.;ThemeIcon=>editor.ThemeIcon): Token
#include(vs/editor/common/languages/language): ILanguageExtensionPoint
#includeAll(vs/editor/standalone/common/monarch/monarchTypes):

}

declare namespace monaco.worker {

#include(vs/editor/common/model/mirrorTextModel): IMirrorTextModel
#includeAll(vs/editor/common/services/editorWebWorker;):

}

//dtsv=3
