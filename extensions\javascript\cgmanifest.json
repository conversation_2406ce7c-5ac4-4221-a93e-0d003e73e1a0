{"registrations": [{"component": {"type": "git", "git": {"name": "microsoft/TypeScript-TmLanguage", "repositoryUrl": "https://github.com/microsoft/TypeScript-TmLanguage", "commitHash": "3133e3d914db9a2bb8812119f9273727a305f16b"}}, "license": "MIT", "version": "0.0.1", "description": "The file syntaxes/JavaScript.tmLanguage.json was derived from TypeScriptReact.tmLanguage in https://github.com/microsoft/TypeScript-TmLanguage."}, {"component": {"type": "git", "git": {"name": "textmate/javascript.tmbundle", "repositoryUrl": "https://github.com/textmate/javascript.tmbundle", "commitHash": "fccf0af0c95430a42e1bf98f0c7a4723a53283e7"}}, "licenseDetail": ["Copyright (c) textmate-javascript.tmbundle project authors", "", "If not otherwise specified (see below), files in this repository fall under the following license:", "", "Permission to copy, use, modify, sell and distribute this", "software is granted. This software is provided \"as is\" without", "express or implied warranty, and with no claim as to its", "suitability for any purpose.", "", "An exception is made for files in readable text which contain their own license information,", "or files where an accompanying file exists (in the same directory) with a \"-license\" suffix added", "to the base-name name of the original file, and an extension of txt, html, or similar. For example", "\"tidy\" is accompanied by \"tidy-license.txt\"."], "license": "TextMate Bundle License", "version": "0.0.0"}], "version": 1}