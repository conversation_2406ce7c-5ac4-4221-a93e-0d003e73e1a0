[{"kind": 1, "language": "markdown", "value": "## Config"}, {"kind": 2, "language": "github-issues", "value": "$since=2021-10-01"}, {"kind": 1, "language": "markdown", "value": "# vscode\n\nQuery exceeds the maximum result. Run the query manually: `is:issue is:open closed:>2021-10-01`"}, {"kind": 2, "language": "github-issues", "value": "//repo:microsoft/vscode is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "//repo:microsoft/vscode is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-remote-release"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-remote-release is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-remote-release is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-editor"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-editor is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-editor is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-docs"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-docs is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-docs is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-js-debug"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-js-debug is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-js-debug is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# language-server-protocol"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/language-server-protocol is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/language-server-protocol is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-eslint"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-eslint is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-eslint is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-css-languageservice"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-css-languageservice is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-css-languageservice is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-test"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-test is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-test is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-pull-request-github"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-pull-request-github is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-test is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-chrome-debug-core"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-chrome-debug-core is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-chrome-debug-core is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-debugadapter-node"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-debugadapter-node is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-debugadapter-node is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-emmet-helper"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-emmet-helper is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-emmet-helper is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-extension-vscode\n\nDeprecated"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-extension-vscode is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-extension-vscode is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-extension-samples"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-extension-samples is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-extension-samples is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-filewatcher-windows"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-filewatcher-windows is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-filewatcher-windows is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-generator-code"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-generator-code is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-generator-code is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-html-languageservice"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-html-languageservice is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-html-languageservice is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-json-languageservice"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-json-languageservice is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-json-languageservice is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-languageserver-node"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-languageserver-node is:issue closed:>$since"}, {"kind": 1, "language": "markdown", "value": ""}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-languageserver-node is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-loader"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-loader is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-loader is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-mono-debug"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-mono-debug is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-mono-debug is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-node-debug"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-node-debug is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-node-debug is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-node-debug2"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-node-debug2 is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-node-debug2 is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-recipes"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-recipes is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-recipes is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-textmate"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-textmate is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-textmate is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-themes"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-themes is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-themes is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-vsce"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-vsce is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-vsce is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-website"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-website is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-website is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-windows-process-tree"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-windows-process-tree is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-windows-process-tree is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# debug-adapter-protocol"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/debug-adapter-protocol is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/debug-adapter-protocol is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# inno-updater"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/inno-updater is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/inno-updater is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-languages"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-languages is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-languages is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-typescript"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-typescript is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-typescript is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-css"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-css is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-css is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-json"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-json is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-json is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-html"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-html is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-html is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# monaco-editor-webpack-plugin"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-editor-webpack-plugin is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/monaco-editor-webpack-plugin is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# node-jsonc-parser"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/node-jsonc-parser is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/node-jsonc-parser is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-jupyter"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-jupyter is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-jupyter is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-python"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-python is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-python is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": "# vscode-livepreview"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-livepreview is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-livepreview is:issue created:>$since"}, {"kind": 1, "language": "markdown", "value": ""}, {"kind": 1, "language": "markdown", "value": "# vscode-test"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-test is:issue closed:>$since"}, {"kind": 2, "language": "github-issues", "value": "repo:microsoft/vscode-test is:issue created:>$since"}]