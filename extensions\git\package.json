{"name": "git", "displayName": "%displayName%", "description": "%description%", "publisher": "vscode", "license": "MIT", "version": "1.0.0", "engines": {"vscode": "^1.5.0"}, "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "enabledApiProposals": ["canonicalUri<PERSON>rovider", "contribEditSessions", "contribEditorContentMenu", "contribMergeEditorMenus", "contribMultiDiffEditorMenus", "contribDiffEditorGutterToolBarMenus", "contribSourceControlHistoryItemMenu", "contribSourceControlHistoryTitleMenu", "contribSourceControlInputBoxMenu", "contribSourceControlTitleMenu", "contribViewsWelcome", "editSessionIdentityProvider", "quickDiffProvider", "quickInputButtonLocation", "quickPickSortByLabel", "scmActionButton", "scm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scmMultiDiffEditor", "scmSelectedProvider", "scmTextDocument", "scmValidation", "statusBarItemTooltip", "tabInputMultiDiff", "tabInputTextMerge", "textDocumentEncoding", "textEditorDiffInformation", "timeline"], "categories": ["Other"], "activationEvents": ["*", "onEditSession:file", "onFileSystem:git", "onFileSystem:git-show"], "extensionDependencies": ["vscode.git-base"], "main": "./out/main", "icon": "resources/icons/git.png", "scripts": {"compile": "gulp compile-extension:git", "watch": "gulp watch-extension:git", "update-emoji": "node ./build/update-emoji.js", "test": "node ../../node_modules/mocha/bin/mocha"}, "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": false}}, "contributes": {"commands": [{"command": "git.continueInLocalClone", "title": "%command.continueInLocalClone%", "category": "Git", "icon": "$(repo-clone)", "enablement": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && remoteName"}, {"command": "git.clone", "title": "%command.clone%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.cloneRecursive", "title": "%command.cloneRecursive%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.init", "title": "%command.init%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.openRepository", "title": "%command.openRepository%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.reopenClosedRepositories", "title": "%command.reopenClosedRepositories%", "icon": "$(repo)", "category": "Git", "enablement": "!operationInProgress && git.closedRepositoryCount != 0"}, {"command": "git.close", "title": "%command.close%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.closeOtherRepositories", "title": "%command.closeOtherRepositories%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.refresh", "title": "%command.refresh%", "category": "Git", "icon": "$(refresh)", "enablement": "!operationInProgress"}, {"command": "git.openChange", "title": "%command.openChange%", "category": "Git", "icon": "$(compare-changes)"}, {"command": "git.openAllChanges", "title": "%command.openAllChanges%", "category": "Git"}, {"command": "git.openFile", "title": "%command.openFile%", "category": "Git", "icon": "$(go-to-file)"}, {"command": "git.openFile2", "title": "%command.openFile%", "category": "Git", "icon": "$(go-to-file)"}, {"command": "git.openHEADFile", "title": "%command.openHEADFile%", "category": "Git"}, {"command": "git.stage", "title": "%command.stage%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.stageAll", "title": "%command.stageAll%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.stageAllTracked", "title": "%command.stageAllTracked%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.stageAllUntracked", "title": "%command.stageAllUntracked%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.stageAllMerge", "title": "%command.stageAllMerge%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.stageSelectedRanges", "title": "%command.stageSelectedRanges%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.diff.stageHunk", "title": "%command.stageBlock%", "category": "Git", "icon": "$(plus)"}, {"command": "git.diff.stageSelection", "title": "%command.stageSelection%", "category": "Git", "icon": "$(plus)"}, {"command": "git.revertSelectedRanges", "title": "%command.revertSelectedRanges%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stage<PERSON>hange", "title": "%command.stageChange%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.stageFile", "title": "%command.stage%", "category": "Git", "icon": "$(add)", "enablement": "!operationInProgress"}, {"command": "git.revertChange", "title": "%command.revertChange%", "category": "Git", "icon": "$(discard)", "enablement": "!operationInProgress"}, {"command": "git.unstage", "title": "%command.unstage%", "category": "Git", "icon": "$(remove)", "enablement": "!operationInProgress"}, {"command": "git.unstageAll", "title": "%command.unstageAll%", "category": "Git", "icon": "$(remove)", "enablement": "!operationInProgress"}, {"command": "git.unstageSelectedRanges", "title": "%command.unstageSelectedRanges%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.unstageFile", "title": "%command.unstage%", "category": "Git", "icon": "$(remove)", "enablement": "!operationInProgress"}, {"command": "git.clean", "title": "%command.clean%", "category": "Git", "icon": "$(discard)", "enablement": "!operationInProgress"}, {"command": "git.cleanAll", "title": "%command.cleanAll%", "category": "Git", "icon": "$(discard)", "enablement": "!operationInProgress"}, {"command": "git.cleanAllTracked", "title": "%command.cleanAllTracked%", "category": "Git", "icon": "$(discard)", "enablement": "!operationInProgress"}, {"command": "git.cleanAllUntracked", "title": "%command.cleanAllUntracked%", "category": "Git", "icon": "$(discard)", "enablement": "!operationInProgress"}, {"command": "git.rename", "title": "%command.rename%", "category": "Git", "icon": "$(discard)", "enablement": "!operationInProgress"}, {"command": "git.commit", "title": "%command.commit%", "category": "Git", "icon": "$(check)", "enablement": "!operationInProgress"}, {"command": "git.commitAmend", "title": "%command.commitAmend%", "category": "Git", "icon": "$(check)", "enablement": "!operationInProgress"}, {"command": "git.commitSigned", "title": "%command.commitSigned%", "category": "Git", "icon": "$(check)", "enablement": "!operationInProgress"}, {"command": "git.commitStaged", "title": "%command.commitStaged%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitEmpty", "title": "%command.commitEmpty%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitStagedSigned", "title": "%command.commitStagedSigned%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitStagedAmend", "title": "%command.commitStagedAmend%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAll", "title": "%command.commitAll%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAllSigned", "title": "%command.commitAllSigned%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAllAmend", "title": "%command.commitAllAmend%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitNoVerify", "title": "%command.commitNoVerify%", "category": "Git", "icon": "$(check)", "enablement": "!operationInProgress"}, {"command": "git.commitStagedNoVerify", "title": "%command.commitStagedNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitEmptyNoVerify", "title": "%command.commitEmptyNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitStagedSignedNoVerify", "title": "%command.commitStagedSignedNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAmendNoVerify", "title": "%command.commitAmendNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitSignedNoVerify", "title": "%command.commitSignedNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitStagedAmendNoVerify", "title": "%command.commitStagedAmendNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAllNoVerify", "title": "%command.commitAllNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAllSignedNoVerify", "title": "%command.commitAllSignedNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitAllAmendNoVerify", "title": "%command.commitAllAmendNoVerify%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.commitMessageAccept", "title": "%command.commitMessageAccept%", "icon": "$(check)", "category": "Git"}, {"command": "git.commitMessageDiscard", "title": "%command.commitMessageDiscard%", "icon": "$(discard)", "category": "Git"}, {"command": "git.restoreCommitTemplate", "title": "%command.restoreCommitTemplate%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.undoCommit", "title": "%command.undoCommit%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.checkout", "title": "%command.checkout%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.graph.checkout", "title": "%command.graphCheckout%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.checkoutDetached", "title": "%command.checkoutDetached%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.graph.checkoutDetached", "title": "%command.graphCheckoutDetached%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.branch", "title": "%command.branch%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.branchFrom", "title": "%command.branchFrom%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.deleteBranch", "title": "%command.deleteBranch%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.graph.deleteBranch", "title": "%command.graphDeleteBranch%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.deleteRemoteBranch", "title": "%command.deleteRemoteBranch%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.renameBranch", "title": "%command.renameBranch%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.merge", "title": "%command.merge%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.mergeAbort", "title": "%command.mergeAbort%", "category": "Git", "enablement": "gitMergeInProgress"}, {"command": "git.rebase", "title": "%command.rebase%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.createTag", "title": "%command.createTag%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.deleteTag", "title": "%command.deleteTag%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.graph.deleteTag", "title": "%command.graphDeleteTag%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.deleteRemoteTag", "title": "%command.deleteRemoteTag%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.fetch", "title": "%command.fetch%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.fetch<PERSON>rune", "title": "%command.fetchPrune%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.fetchAll", "title": "%command.fetchAll%", "icon": "$(git-fetch)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.fetchRef", "title": "%command.fetch%", "icon": "$(git-fetch)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pull", "title": "%command.pull%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pullRebase", "title": "%command.pullRebase%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pullFrom", "title": "%command.pullFrom%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pullRef", "title": "%command.pull%", "icon": "$(repo-pull)", "category": "Git", "enablement": "!operationInProgress && scmCurrentHistoryItemRefInFilter && scmCurrentHistoryItemRefHasRemote"}, {"command": "git.push", "title": "%command.push%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushForce", "title": "%command.pushForce%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushTo", "title": "%command.pushTo%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushToForce", "title": "%command.pushToForce%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushTags", "title": "%command.pushTags%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushWithTags", "title": "%command.pushFollowTags%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushWithTagsForce", "title": "%command.pushFollowTagsForce%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.pushRef", "title": "%command.push%", "icon": "$(repo-push)", "category": "Git", "enablement": "!operationInProgress && scmCurrentHistoryItemRefInFilter && scmCurrentHistoryItemRefHasRemote"}, {"command": "git.cherry<PERSON>ick", "title": "%command.cherryPick%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.graph.cherryPick", "title": "%command.graphCherryPick%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.cherryPickAbort", "title": "%command.cherryPickAbort%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.addRemote", "title": "%command.addRemote%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.removeRemote", "title": "%command.removeRemote%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.sync", "title": "%command.sync%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.syncRebase", "title": "%command.syncRebase%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.publish", "title": "%command.publish%", "category": "Git", "icon": "$(cloud-upload)", "enablement": "!operationInProgress"}, {"command": "git.showOutput", "title": "%command.showOutput%", "category": "Git"}, {"command": "git.ignore", "title": "%command.ignore%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.revealInExplorer", "title": "%command.revealInExplorer%", "category": "Git"}, {"command": "git.revealFileInOS.linux", "title": "%command.revealFileInOS.linux%", "category": "Git"}, {"command": "git.revealFileInOS.mac", "title": "%command.revealFileInOS.mac%", "category": "Git"}, {"command": "git.revealFileInOS.windows", "title": "%command.revealFileInOS.windows%", "category": "Git"}, {"command": "git.stashIncludeUntracked", "title": "%command.stashIncludeUntracked%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stash", "title": "%command.stash%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashStaged", "title": "%command.stashStaged%", "category": "Git", "enablement": "!operationInProgress && gitVersion2.35"}, {"command": "git.stashPop", "title": "%command.stashPop%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashPopLatest", "title": "%command.stashPopLatest%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashPopEditor", "title": "%command.stashPopEditor%", "icon": "$(git-stash-pop)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashApply", "title": "%command.stashApply%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashApplyLatest", "title": "%command.stashApplyLatest%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashApplyEditor", "title": "%command.stashApplyEditor%", "icon": "$(git-stash-apply)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashDrop", "title": "%command.stashDrop%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashDropAll", "title": "%command.stashDropAll%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashDropEditor", "title": "%command.stashDropEditor%", "icon": "$(trash)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.stashView", "title": "%command.stashView%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.timeline.openDiff", "title": "%command.timelineOpenDiff%", "icon": "$(compare-changes)", "category": "Git"}, {"command": "git.timeline.copyCommitId", "title": "%command.timelineCopyCommitId%", "category": "Git"}, {"command": "git.timeline.copyCommitMessage", "title": "%command.timelineCopyCommitMessage%", "category": "Git"}, {"command": "git.timeline.selectForCompare", "title": "%command.timelineSelectForCompare%", "category": "Git"}, {"command": "git.timeline.compareWithSelected", "title": "%command.timelineCompareWithSelected%", "category": "Git"}, {"command": "git.timeline.viewCommit", "title": "%command.viewCommit%", "icon": "$(diff-multiple)", "category": "Git"}, {"command": "git.rebaseAbort", "title": "%command.rebaseAbort%", "category": "Git", "enablement": "gitRebaseInProgress"}, {"command": "git.closeAllDiffEditors", "title": "%command.closeAllDiffEditors%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.closeAllUnmodifiedEditors", "title": "%command.closeAllUnmodifiedEditors%", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.api.getRepositories", "title": "%command.api.getRepositories%", "category": "Git API"}, {"command": "git.api.getRepositoryState", "title": "%command.api.getRepositoryState%", "category": "Git API"}, {"command": "git.api.getRemoteSources", "title": "%command.api.getRemoteSources%", "category": "Git API"}, {"command": "git.acceptMerge", "title": "%command.git.acceptMerge%", "category": "Git", "enablement": "isMergeEditor && mergeEditorResultUri in git.mergeChanges"}, {"command": "git.openMergeEditor", "title": "%command.git.openMergeEditor%", "category": "Git"}, {"command": "git.runGitMerge", "title": "%command.git.runGitMerge%", "category": "Git", "enablement": "isMergeEditor"}, {"command": "git.runGitMergeDiff3", "title": "%command.git.runGitMergeDiff3%", "category": "Git", "enablement": "isMergeEditor"}, {"command": "git.manageUnsafeRepositories", "title": "%command.manageUnsafeRepositories%", "category": "Git"}, {"command": "git.openRepositoriesInParentFolders", "title": "%command.openRepositoriesInParentFolders%", "category": "Git"}, {"command": "git.viewChanges", "title": "%command.viewChanges%", "icon": "$(diff-multiple)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.viewStagedChanges", "title": "%command.viewStagedChanges%", "icon": "$(diff-multiple)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.viewUntrackedChanges", "title": "%command.viewUntrackedChanges%", "icon": "$(diff-multiple)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.viewCommit", "title": "%command.viewCommit%", "icon": "$(diff-multiple)", "category": "Git", "enablement": "!operationInProgress"}, {"command": "git.copyCommitId", "title": "%command.timelineCopyCommitId%", "category": "Git"}, {"command": "git.copyCommitMessage", "title": "%command.timelineCopyCommitMessage%", "category": "Git"}, {"command": "git.blame.toggleEditorDecoration", "title": "%command.blameToggleEditorDecoration%", "category": "Git"}, {"command": "git.blame.toggleStatusBarItem", "title": "%command.blameToggleStatusBarItem%", "category": "Git"}], "continueEditSession": [{"command": "git.continueInLocalClone", "qualifiedName": "%command.continueInLocalClone.qualifiedName%", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && remoteName", "remoteGroup": "remote_42_git_0_local@0"}], "keybindings": [{"command": "git.stageSelectedRanges", "key": "ctrl+k ctrl+alt+s", "mac": "cmd+k cmd+alt+s", "when": "editorTextFocus && resourceScheme == file"}, {"command": "git.unstageSelectedRanges", "key": "ctrl+k ctrl+n", "mac": "cmd+k cmd+n", "when": "editorTextFocus && isInDiffEditor && isInDiffRightEditor && resourceScheme == git"}, {"command": "git.revertSelectedRanges", "key": "ctrl+k ctrl+r", "mac": "cmd+k cmd+r", "when": "editorTextFocus && resourceScheme == file"}], "menus": {"commandPalette": [{"command": "git.continueInLocalClone", "when": "false"}, {"command": "git.clone", "when": "config.git.enabled && !git.missing"}, {"command": "git.cloneRecursive", "when": "config.git.enabled && !git.missing"}, {"command": "git.init", "when": "config.git.enabled && !git.missing && remoteName != 'codespaces'"}, {"command": "git.openRepository", "when": "config.git.enabled && !git.missing"}, {"command": "git.close", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.closeOtherRepositories", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount > 1"}, {"command": "git.refresh", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.openFile", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == file && scmActiveResourceHasChanges"}, {"command": "git.openHEADFile", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == file && scmActiveResourceHasChanges"}, {"command": "git.openChange", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stage", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stageAll", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stageAllTracked", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stageAllUntracked", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stageAllMerge", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stageSelectedRanges", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == file"}, {"command": "git.stage<PERSON>hange", "when": "false"}, {"command": "git.revertSelectedRanges", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == file"}, {"command": "git.revertChange", "when": "false"}, {"command": "git.openFile2", "when": "false"}, {"command": "git.unstage", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.unstageAll", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.unstageSelectedRanges", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == git"}, {"command": "git.clean", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.cleanAll", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.cleanAllTracked", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.cleanAllUntracked", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.rename", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == file && scmActiveResourceRepository"}, {"command": "git.commit", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitAmend", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitSigned", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitStaged", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitEmpty", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitStagedSigned", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitStagedAmend", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitAll", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitAllSigned", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.commitAllAmend", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.rebaseAbort", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && gitRebaseInProgress"}, {"command": "git.commitNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitStagedNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitEmptyNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitStagedSignedNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitAmendNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitSignedNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitStagedAmendNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitAllNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitAllSignedNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.commitAllAmendNoVerify", "when": "config.git.enabled && !git.missing && config.git.allowNoVerifyCommit && gitOpenRepositoryCount != 0"}, {"command": "git.restoreCommitTemplate", "when": "false"}, {"command": "git.commitMessageAccept", "when": "false"}, {"command": "git.commitMessageDiscard", "when": "false"}, {"command": "git.revealInExplorer", "when": "false"}, {"command": "git.revealFileInOS.linux", "when": "false"}, {"command": "git.revealFileInOS.mac", "when": "false"}, {"command": "git.revealFileInOS.windows", "when": "false"}, {"command": "git.undoCommit", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.checkout", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.branch", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.branchFrom", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.deleteBranch", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.deleteRemoteBranch", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.renameBranch", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.cherry<PERSON>ick", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.cherryPickAbort", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && gitCherryPickInProgress"}, {"command": "git.pull", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.pullFrom", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.pullRebase", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.merge", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.mergeAbort", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && gitMergeInProgress"}, {"command": "git.rebase", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.createTag", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.deleteTag", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.deleteRemoteTag", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.fetch", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.fetch<PERSON>rune", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.fetchAll", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.push", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.pushForce", "when": "config.git.enabled && !git.missing && config.git.allowForcePush && gitOpenRepositoryCount != 0"}, {"command": "git.pushTo", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.pushToForce", "when": "config.git.enabled && !git.missing && config.git.allowForcePush && gitOpenRepositoryCount != 0"}, {"command": "git.pushWithTags", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.pushWithTagsForce", "when": "config.git.enabled && !git.missing && config.git.allowForcePush && gitOpenRepositoryCount != 0"}, {"command": "git.pushTags", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.addRemote", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.removeRemote", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.sync", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.syncRebase", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.publish", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.showOutput", "when": "config.git.enabled"}, {"command": "git.ignore", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && resourceScheme == file && scmActiveResourceRepository"}, {"command": "git.stashIncludeUntracked", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stash", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashStaged", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && gitVersion2.35"}, {"command": "git.stashPop", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashPopLatest", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashPopEditor", "when": "false"}, {"command": "git.stashApply", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashApplyLatest", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashApplyEditor", "when": "false"}, {"command": "git.stashDrop", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashDropAll", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.stashDropEditor", "when": "false"}, {"command": "git.timeline.openDiff", "when": "false"}, {"command": "git.timeline.copyCommitId", "when": "false"}, {"command": "git.timeline.copyCommitMessage", "when": "false"}, {"command": "git.timeline.selectForCompare", "when": "false"}, {"command": "git.timeline.compareWithSelected", "when": "false"}, {"command": "git.timeline.viewCommit", "when": "false"}, {"command": "git.closeAllDiffEditors", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0"}, {"command": "git.api.getRepositories", "when": "false"}, {"command": "git.api.getRepositoryState", "when": "false"}, {"command": "git.api.getRemoteSources", "when": "false"}, {"command": "git.openMergeEditor", "when": "false"}, {"command": "git.manageUnsafeRepositories", "when": "config.git.enabled && !git.missing && git.unsafeRepositoryCount != 0"}, {"command": "git.openRepositoriesInParentFolders", "when": "config.git.enabled && !git.missing && git.parentRepositoryCount != 0"}, {"command": "git.stashView", "when": "config.git.enabled && !git.missing && config.multiDiffEditor.experimental.enabled"}, {"command": "git.viewChanges", "when": "config.git.enabled && !git.missing && config.multiDiffEditor.experimental.enabled"}, {"command": "git.viewStagedChanges", "when": "config.git.enabled && !git.missing && config.multiDiffEditor.experimental.enabled"}, {"command": "git.viewUntrackedChanges", "when": "config.git.enabled && !git.missing && config.multiDiffEditor.experimental.enabled && config.git.untrackedChanges == separate"}, {"command": "git.viewCommit", "when": "false"}, {"command": "git.stageFile", "when": "false"}, {"command": "git.unstageFile", "when": "false"}, {"command": "git.fetchRef", "when": "false"}, {"command": "git.pullRef", "when": "false"}, {"command": "git.pushRef", "when": "false"}, {"command": "git.copyCommitId", "when": "false"}, {"command": "git.copyCommitMessage", "when": "false"}, {"command": "git.graph.checkout", "when": "false"}, {"command": "git.graph.checkoutDetached", "when": "false"}, {"command": "git.graph.deleteBranch", "when": "false"}, {"command": "git.graph.deleteTag", "when": "false"}, {"command": "git.graph.cherryPick", "when": "false"}, {"command": "git.diff.stageHunk", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && diffEditorOriginalUri =~ /^git\\:.*%22ref%22%3A%22~%22%7D$/"}, {"command": "git.diff.stageSelection", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && diffEditorOriginalUri =~ /^git\\:.*%22ref%22%3A%22~%22%7D$/"}], "scm/title": [{"command": "git.commit", "group": "navigation", "when": "scmProvider == git"}, {"command": "git.refresh", "group": "navigation", "when": "scmProvider == git"}, {"command": "git.pull", "group": "1_header@1", "when": "scmProvider == git"}, {"command": "git.push", "group": "1_header@2", "when": "scmProvider == git"}, {"command": "git.clone", "group": "1_header@3", "when": "scmProvider == git"}, {"command": "git.checkout", "group": "1_header@4", "when": "scmProvider == git"}, {"command": "git.fetch", "group": "1_header@5", "when": "scmProvider == git"}, {"submenu": "git.commit", "group": "2_main@1", "when": "scmProvider == git"}, {"submenu": "git.changes", "group": "2_main@2", "when": "scmProvider == git"}, {"submenu": "git.pullpush", "group": "2_main@3", "when": "scmProvider == git"}, {"submenu": "git.branch", "group": "2_main@4", "when": "scmProvider == git"}, {"submenu": "git.remotes", "group": "2_main@5", "when": "scmProvider == git"}, {"submenu": "git.stash", "group": "2_main@6", "when": "scmProvider == git"}, {"submenu": "git.tags", "group": "2_main@7", "when": "scmProvider == git"}, {"command": "git.showOutput", "group": "3_footer", "when": "scmProvider == git"}], "scm/sourceControl/title": [{"command": "git.reopenClosedRepositories", "group": "navigation@1", "when": "git.closedRepositoryCount > 0"}], "scm/sourceControl": [{"command": "git.close", "group": "navigation@1", "when": "scmProvider == git"}, {"command": "git.closeOtherRepositories", "group": "navigation@2", "when": "scmProvider == git && gitOpenRepositoryCount > 1"}], "scm/resourceGroup/context": [{"command": "git.stageAllMerge", "when": "scmProvider == git && scmResourceGroup == merge", "group": "1_modification"}, {"command": "git.stageAllMerge", "when": "scmProvider == git && scmResourceGroup == merge", "group": "inline@2"}, {"command": "git.unstageAll", "when": "scmProvider == git && scmResourceGroup == index", "group": "1_modification"}, {"command": "git.unstageAll", "when": "scmProvider == git && scmResourceGroup == index", "group": "inline@2"}, {"command": "git.viewStagedChanges", "when": "scmProvider == git && scmResourceGroup == index && config.multiDiffEditor.experimental.enabled", "group": "inline@1"}, {"command": "git.viewChanges", "when": "scmProvider == git && scmResourceGroup == workingTree && config.multiDiffEditor.experimental.enabled", "group": "inline@1"}, {"command": "git.cleanAll", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges == mixed", "group": "1_modification"}, {"command": "git.stageAll", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges == mixed", "group": "1_modification"}, {"command": "git.cleanAll", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges == mixed", "group": "inline@2"}, {"command": "git.stageAll", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges == mixed", "group": "inline@2"}, {"command": "git.cleanAllTracked", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges != mixed", "group": "1_modification"}, {"command": "git.stageAllTracked", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges != mixed", "group": "1_modification"}, {"command": "git.cleanAllTracked", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges != mixed", "group": "inline@2"}, {"command": "git.stageAllTracked", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.untrackedChanges != mixed", "group": "inline@2"}, {"command": "git.cleanAllUntracked", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification"}, {"command": "git.stageAllUntracked", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification"}, {"command": "git.viewUntrackedChanges", "when": "scmProvider == git && scmResourceGroup == untracked && config.multiDiffEditor.experimental.enabled", "group": "inline@1"}, {"command": "git.cleanAllUntracked", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "inline@2"}, {"command": "git.stageAllUntracked", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "inline@2"}], "scm/resourceFolder/context": [{"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == merge", "group": "1_modification"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == merge", "group": "inline@2"}, {"command": "git.unstage", "when": "scmProvider == git && scmResourceGroup == index", "group": "1_modification"}, {"command": "git.unstage", "when": "scmProvider == git && scmResourceGroup == index", "group": "inline@2"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "inline@2"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "inline@2"}, {"command": "git.ignore", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "1_modification@3"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "inline@2"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "inline@2"}, {"command": "git.ignore", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification@3"}], "scm/resourceState/context": [{"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == merge", "group": "1_modification"}, {"command": "git.openFile", "when": "scmProvider == git && scmResourceGroup == merge", "group": "navigation"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == merge", "group": "inline@2"}, {"command": "git.revealFileInOS.linux", "when": "scmProvider == git && scmResourceGroup == merge && remoteName == '' && isLinux", "group": "2_view@1"}, {"command": "git.revealFileInOS.mac", "when": "scmProvider == git && scmResourceGroup == merge && remoteName == '' && isMac", "group": "2_view@1"}, {"command": "git.revealFileInOS.windows", "when": "scmProvider == git && scmResourceGroup == merge && remoteName == '' && isWindows", "group": "2_view@1"}, {"command": "git.revealInExplorer", "when": "scmProvider == git && scmResourceGroup == merge", "group": "2_view@2"}, {"command": "git.openFile2", "when": "scmProvider == git && scmResourceGroup == merge && config.git.showInlineOpenFileAction && config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == merge && config.git.showInlineOpenFileAction && !config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == index", "group": "navigation"}, {"command": "git.openFile", "when": "scmProvider == git && scmResourceGroup == index", "group": "navigation"}, {"command": "git.openHEADFile", "when": "scmProvider == git && scmResourceGroup == index", "group": "navigation"}, {"command": "git.unstage", "when": "scmProvider == git && scmResourceGroup == index", "group": "1_modification"}, {"command": "git.unstage", "when": "scmProvider == git && scmResourceGroup == index", "group": "inline@2"}, {"command": "git.revealFileInOS.linux", "when": "scmProvider == git && scmResourceGroup == index && remoteName == '' && isLinux", "group": "2_view@1"}, {"command": "git.revealFileInOS.mac", "when": "scmProvider == git && scmResourceGroup == index && remoteName == '' && isMac", "group": "2_view@1"}, {"command": "git.revealFileInOS.windows", "when": "scmProvider == git && scmResourceGroup == index && remoteName == '' && isWindows", "group": "2_view@1"}, {"command": "git.revealInExplorer", "when": "scmProvider == git && scmResourceGroup == index", "group": "2_view@2"}, {"command": "git.openFile2", "when": "scmProvider == git && scmResourceGroup == index && config.git.showInlineOpenFileAction && config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == index && config.git.showInlineOpenFileAction && !config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "navigation"}, {"command": "git.openHEADFile", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "navigation"}, {"command": "git.openFile", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "navigation"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "inline@2"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "inline@2"}, {"command": "git.openFile2", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.showInlineOpenFileAction && config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == workingTree && config.git.showInlineOpenFileAction && !config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.ignore", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "1_modification@3"}, {"command": "git.revealFileInOS.linux", "when": "scmProvider == git && scmResourceGroup == workingTree && remoteName == '' && isLinux", "group": "2_view@1"}, {"command": "git.revealFileInOS.mac", "when": "scmProvider == git && scmResourceGroup == workingTree && remoteName == '' && isMac", "group": "2_view@1"}, {"command": "git.revealFileInOS.windows", "when": "scmProvider == git && scmResourceGroup == workingTree && remoteName == '' && isWindows", "group": "2_view@1"}, {"command": "git.revealInExplorer", "when": "scmProvider == git && scmResourceGroup == workingTree", "group": "2_view@2"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "navigation"}, {"command": "git.openHEADFile", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "navigation"}, {"command": "git.openFile", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "navigation"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == untracked && !gitFreshRepository", "group": "1_modification"}, {"command": "git.clean", "when": "scmProvider == git && scmResourceGroup == untracked && !gitFreshRepository", "group": "inline@2"}, {"command": "git.stage", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "inline@2"}, {"command": "git.openFile2", "when": "scmProvider == git && scmResourceGroup == untracked && config.git.showInlineOpenFileAction && config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.openChange", "when": "scmProvider == git && scmResourceGroup == untracked && config.git.showInlineOpenFileAction && !config.git.openDiffOnClick", "group": "inline@1"}, {"command": "git.ignore", "when": "scmProvider == git && scmResourceGroup == untracked", "group": "1_modification@3"}], "scm/history/title": [{"command": "git.fetchAll", "group": "navigation@900", "when": "scmProvider == git"}, {"command": "git.pullRef", "group": "navigation@901", "when": "scmProvider == git"}, {"command": "git.pushRef", "when": "scmProvider == git && scmCurrentHistoryItemRefHasRemote", "group": "navigation@902"}, {"command": "git.publish", "when": "scmProvider == git && !scmCurrentHistoryItemRefHasRemote", "group": "navigation@903"}], "scm/historyItem/context": [{"command": "git.graph.checkoutDetached", "when": "scmProvider == git", "group": "1_checkout@2"}, {"command": "git.branch", "when": "scmProvider == git", "group": "2_branch@2"}, {"command": "git.createTag", "when": "scmProvider == git", "group": "3_tag@1"}, {"command": "git.graph.cherryPick", "when": "scmProvider == git", "group": "4_modify@1"}, {"command": "git.copyCommitId", "when": "scmProvider == git && !listMultiSelection", "group": "9_copy@1"}, {"command": "git.copyCommitMessage", "when": "scmProvider == git && !listMultiSelection", "group": "9_copy@2"}], "scm/historyItemRef/context": [{"command": "git.graph.checkout", "when": "scmProvider == git", "group": "1_checkout@1"}, {"command": "git.graph.deleteBranch", "when": "scmProvider == git && scmHistoryItemRef =~ /^refs\\/heads\\/|^refs\\/remotes\\//", "group": "2_branch@2"}, {"command": "git.graph.deleteTag", "when": "scmProvider == git && scmHistoryItemRef =~ /^refs\\/tags\\//", "group": "3_tag@2"}], "editor/title": [{"command": "git.openFile", "group": "navigation", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && resourceScheme =~ /^git$|^file$/"}, {"command": "git.openFile", "group": "navigation", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInNotebookTextDiffEditor && resourceScheme =~ /^git$|^file$/"}, {"command": "git.openChange", "group": "navigation@2", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && !isInDiffEditor && !isMergeEditor && resourceScheme == file && scmActiveResourceHasChanges"}, {"command": "git.commitMessageAccept", "group": "navigation", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && editorLangId == git-commit"}, {"command": "git.commitMessageDiscard", "group": "navigation", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && editorLangId == git-commit"}, {"command": "git.stashApplyEditor", "alt": "git.stashPopEditor", "group": "navigation@1", "when": "config.git.enabled && !git.missing && resourceScheme == git-stash"}, {"command": "git.stashDropEditor", "group": "navigation@2", "when": "config.git.enabled && !git.missing && resourceScheme == git-stash"}, {"command": "git.stage", "group": "2_git@1", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && !isInDiffEditor && !isMergeEditor && resourceScheme == file && git.activeResourceHasUnstagedChanges"}, {"command": "git.unstage", "group": "2_git@2", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && !isInDiffEditor && !isMergeEditor && resourceScheme == file && git.activeResourceHasStagedChanges"}, {"command": "git.stage", "group": "2_git@1", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == file"}, {"command": "git.stageSelectedRanges", "group": "2_git@2", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == file"}, {"command": "git.unstage", "group": "2_git@3", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == git"}, {"command": "git.unstageSelectedRanges", "group": "2_git@4", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == git"}, {"command": "git.revertSelectedRanges", "group": "2_git@5", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == file"}], "editor/context": [{"command": "git.stageSelectedRanges", "group": "2_git@1", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == file"}, {"command": "git.unstageSelectedRanges", "group": "2_git@2", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == git"}, {"command": "git.revertSelectedRanges", "group": "2_git@3", "when": "config.git.enabled && !git.missing && gitOpenRepositoryCount != 0 && isInDiffEditor && isInDiffRightEditor && !isEmbeddedDiffEditor && resourceScheme == file"}], "editor/content": [{"command": "git.acceptMerge", "when": "isMergeResultEditor && mergeEditorBaseUri =~ /^(git|file):/ && mergeEditorResultUri in git.mergeChanges"}, {"command": "git.openMergeEditor", "group": "navigation@-10", "when": "config.git.enabled && !git.missing && !isInDiffEditor && !isMergeEditor && resource in git.mergeChanges"}], "multiDiffEditor/resource/title": [{"command": "git.stageFile", "group": "navigation", "when": "scmProvider == git && scmResourceGroup == workingTree"}, {"command": "git.stageFile", "group": "navigation", "when": "scmProvider == git && scmResourceGroup == untracked"}, {"command": "git.unstageFile", "group": "navigation", "when": "scmProvider == git && scmResourceGroup == index"}], "diffEditor/gutter/hunk": [{"command": "git.diff.stageHunk", "group": "primary@10", "when": "diffEditorOriginalUri =~ /^git\\:.*%22ref%22%3A%22~%22%7D$/"}], "diffEditor/gutter/selection": [{"command": "git.diff.stageSelection", "group": "primary@10", "when": "diffEditorOriginalUri =~ /^git\\:.*%22ref%22%3A%22~%22%7D$/"}], "scm/change/title": [{"command": "git.stage<PERSON>hange", "when": "config.git.enabled && !git.missing && originalResourceScheme == git"}, {"command": "git.revertChange", "when": "config.git.enabled && !git.missing && originalResourceScheme == git"}], "timeline/item/context": [{"command": "git.timeline.viewCommit", "group": "inline", "when": "config.git.enabled && !git.missing && timelineItem =~ /git:file:commit\\b/ && !listMultiSelection && config.multiDiffEditor.experimental.enabled"}, {"command": "git.timeline.openDiff", "group": "1_actions@1", "when": "config.git.enabled && !git.missing && timelineItem =~ /git:file\\b/ && !listMultiSelection"}, {"command": "git.timeline.viewCommit", "group": "1_actions@2", "when": "config.git.enabled && !git.missing && timelineItem =~ /git:file:commit\\b/ && !listMultiSelection && config.multiDiffEditor.experimental.enabled"}, {"command": "git.timeline.compareWithSelected", "group": "3_compare@1", "when": "config.git.enabled && !git.missing && git.timeline.selectedForCompare && timelineItem =~ /git:file\\b/ && !listMultiSelection"}, {"command": "git.timeline.selectForCompare", "group": "3_compare@2", "when": "config.git.enabled && !git.missing && timelineItem =~ /git:file\\b/ && !listMultiSelection"}, {"command": "git.timeline.copyCommitId", "group": "5_copy@1", "when": "config.git.enabled && !git.missing && timelineItem =~ /git:file:commit\\b/ && !listMultiSelection"}, {"command": "git.timeline.copyCommitMessage", "group": "5_copy@2", "when": "config.git.enabled && !git.missing && timelineItem =~ /git:file:commit\\b/ && !listMultiSelection"}], "git.commit": [{"command": "git.commit", "group": "1_commit@1"}, {"command": "git.commitStaged", "group": "1_commit@2"}, {"command": "git.commitAll", "group": "1_commit@3"}, {"command": "git.undoCommit", "group": "1_commit@4"}, {"command": "git.rebaseAbort", "group": "1_commit@5"}, {"command": "git.commitNoVerify", "group": "2_commit_noverify@1", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitStagedNoVerify", "group": "2_commit_noverify@2", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitAllNoVerify", "group": "2_commit_noverify@3", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitAmend", "group": "3_amend@1"}, {"command": "git.commitStagedAmend", "group": "3_amend@2"}, {"command": "git.commitAllAmend", "group": "3_amend@3"}, {"command": "git.commitAmendNoVerify", "group": "4_amend_noverify@1", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitStagedAmendNoVerify", "group": "4_amend_noverify@2", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitAllAmendNoVerify", "group": "4_amend_noverify@3", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitSigned", "group": "5_signoff@1"}, {"command": "git.commitStagedSigned", "group": "5_signoff@2"}, {"command": "git.commitAllSigned", "group": "5_signoff@3"}, {"command": "git.commitSignedNoVerify", "group": "6_signoff_noverify@1", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitStagedSignedNoVerify", "group": "6_signoff_noverify@2", "when": "config.git.allowNoVerifyCommit"}, {"command": "git.commitAllSignedNoVerify", "group": "6_signoff_noverify@3", "when": "config.git.allowNoVerifyCommit"}], "git.changes": [{"command": "git.stageAll", "group": "changes@1"}, {"command": "git.unstageAll", "group": "changes@2"}, {"command": "git.cleanAll", "group": "changes@3"}], "git.pullpush": [{"command": "git.sync", "group": "1_sync@1"}, {"command": "git.syncRebase", "when": "gitState == idle", "group": "1_sync@2"}, {"command": "git.pull", "group": "2_pull@1"}, {"command": "git.pullRebase", "group": "2_pull@2"}, {"command": "git.pullFrom", "group": "2_pull@3"}, {"command": "git.push", "group": "3_push@1"}, {"command": "git.pushForce", "when": "config.git.allowForcePush", "group": "3_push@2"}, {"command": "git.pushTo", "group": "3_push@3"}, {"command": "git.pushToForce", "when": "config.git.allowForcePush", "group": "3_push@4"}, {"command": "git.fetch", "group": "4_fetch@1"}, {"command": "git.fetch<PERSON>rune", "group": "4_fetch@2"}, {"command": "git.fetchAll", "group": "4_fetch@3"}], "git.branch": [{"command": "git.merge", "group": "1_merge@1"}, {"command": "git.rebase", "group": "1_merge@2"}, {"command": "git.branch", "group": "2_branch@1"}, {"command": "git.branchFrom", "group": "2_branch@2"}, {"command": "git.renameBranch", "group": "3_modify@1"}, {"command": "git.deleteBranch", "group": "3_modify@2"}, {"command": "git.deleteRemoteBranch", "group": "3_modify@3"}, {"command": "git.publish", "group": "4_publish@1"}], "git.remotes": [{"command": "git.addRemote", "group": "remote@1"}, {"command": "git.removeRemote", "group": "remote@2"}], "git.stash": [{"command": "git.stash", "group": "1_stash@1"}, {"command": "git.stashIncludeUntracked", "group": "1_stash@2"}, {"command": "git.stashStaged", "when": "gitVersion2.35", "group": "1_stash@3"}, {"command": "git.stashApplyLatest", "group": "2_apply@1"}, {"command": "git.stashApply", "group": "2_apply@2"}, {"command": "git.stashPopLatest", "group": "3_pop@1"}, {"command": "git.stashPop", "group": "3_pop@2"}, {"command": "git.stashDrop", "group": "4_drop@1"}, {"command": "git.stashDropAll", "group": "4_drop@2"}, {"command": "git.stashView", "when": "config.multiDiffEditor.experimental.enabled", "group": "5_preview@1"}], "git.tags": [{"command": "git.createTag", "group": "tags@1"}, {"command": "git.deleteTag", "group": "tags@2"}, {"command": "git.deleteRemoteTag", "group": "tags@3"}]}, "submenus": [{"id": "git.commit", "label": "%submenu.commit%"}, {"id": "git.changes", "label": "%submenu.changes%"}, {"id": "git.pullpush", "label": "%submenu.pullpush%"}, {"id": "git.branch", "label": "%submenu.branch%"}, {"id": "git.remotes", "label": "%submenu.remotes%"}, {"id": "git.stash", "label": "%submenu.stash%"}, {"id": "git.tags", "label": "%submenu.tags%"}], "configuration": {"title": "Git", "properties": {"git.enabled": {"type": "boolean", "scope": "resource", "description": "%config.enabled%", "default": true}, "git.path": {"type": ["string", "null", "array"], "markdownDescription": "%config.path%", "default": null, "scope": "machine"}, "git.autoRepositoryDetection": {"type": ["boolean", "string"], "enum": [true, false, "subFolders", "openEditors"], "enumDescriptions": ["%config.autoRepositoryDetection.true%", "%config.autoRepositoryDetection.false%", "%config.autoRepositoryDetection.subFolders%", "%config.autoRepositoryDetection.openEditors%"], "description": "%config.autoRepositoryDetection%", "default": true}, "git.autorefresh": {"type": "boolean", "description": "%config.autorefresh%", "default": true}, "git.autofetch": {"type": ["boolean", "string"], "enum": [true, false, "all"], "scope": "resource", "markdownDescription": "%config.autofetch%", "default": false, "tags": ["usesOnlineServices"]}, "git.autofetchPeriod": {"type": "number", "scope": "resource", "markdownDescription": "%config.autofetchPeriod%", "default": 180}, "git.defaultBranchName": {"type": "string", "markdownDescription": "%config.defaultBranchName%", "default": "main", "scope": "resource"}, "git.branchPrefix": {"type": "string", "description": "%config.branchPrefix%", "default": "", "scope": "resource"}, "git.branchProtection": {"type": "array", "markdownDescription": "%config.branchProtection%", "items": {"type": "string"}, "default": [], "scope": "resource"}, "git.branchProtectionPrompt": {"type": "string", "description": "%config.branchProtectionPrompt%", "enum": ["alwaysCommit", "alwaysCommitToNewBranch", "alwaysPrompt"], "enumDescriptions": ["%config.branchProtectionPrompt.alwaysCommit%", "%config.branchProtectionPrompt.alwaysCommitToNewBranch%", "%config.branchProtectionPrompt.alwaysPrompt%"], "default": "alwaysPrompt", "scope": "resource"}, "git.branchValidationRegex": {"type": "string", "description": "%config.branchValidationRegex%", "default": ""}, "git.branchWhitespaceChar": {"type": "string", "description": "%config.branchWhitespaceChar%", "default": "-"}, "git.branchRandomName.enable": {"type": "boolean", "description": "%config.branchRandomNameEnable%", "default": false, "scope": "resource"}, "git.branchRandomName.dictionary": {"type": "array", "markdownDescription": "%config.branchRandomNameDictionary%", "items": {"type": "string", "enum": ["adjectives", "animals", "colors", "numbers"], "enumDescriptions": ["%config.branchRandomNameDictionary.adjectives%", "%config.branchRandomNameDictionary.animals%", "%config.branchRandomNameDictionary.colors%", "%config.branchRandomNameDictionary.numbers%"]}, "minItems": 1, "maxItems": 5, "default": ["adjectives", "animals"], "scope": "resource"}, "git.confirmSync": {"type": "boolean", "description": "%config.confirmSync%", "default": true}, "git.countBadge": {"type": "string", "enum": ["all", "tracked", "off"], "enumDescriptions": ["%config.countBadge.all%", "%config.countBadge.tracked%", "%config.countBadge.off%"], "description": "%config.countBadge%", "default": "all", "scope": "resource"}, "git.checkoutType": {"type": "array", "items": {"type": "string", "enum": ["local", "tags", "remote"], "enumDescriptions": ["%config.checkoutType.local%", "%config.checkoutType.tags%", "%config.checkoutType.remote%"]}, "uniqueItems": true, "markdownDescription": "%config.checkoutType%", "default": ["local", "remote", "tags"]}, "git.ignoreLegacyWarning": {"type": "boolean", "description": "%config.ignoreLegacyWarning%", "default": false}, "git.ignoreMissingGitWarning": {"type": "boolean", "description": "%config.ignoreMissingGitWarning%", "default": false}, "git.ignoreWindowsGit27Warning": {"type": "boolean", "description": "%config.ignoreWindowsGit27Warning%", "default": false}, "git.ignoreLimitWarning": {"type": "boolean", "description": "%config.ignoreLimitWarning%", "default": false}, "git.ignoreRebaseWarning": {"type": "boolean", "description": "%config.ignoreRebaseWarning%", "default": false}, "git.defaultCloneDirectory": {"type": ["string", "null"], "default": null, "scope": "machine", "description": "%config.defaultCloneDirectory%"}, "git.useEditorAsCommitInput": {"type": "boolean", "description": "%config.useEditorAsCommitInput%", "default": true}, "git.verboseCommit": {"type": "boolean", "scope": "resource", "markdownDescription": "%config.verboseCommit%", "default": false}, "git.enableSmartCommit": {"type": "boolean", "scope": "resource", "description": "%config.enableSmartCommit%", "default": false}, "git.smartCommitChanges": {"type": "string", "enum": ["all", "tracked"], "enumDescriptions": ["%config.smartCommitChanges.all%", "%config.smartCommitChanges.tracked%"], "scope": "resource", "description": "%config.smartCommitChanges%", "default": "all"}, "git.suggestSmartCommit": {"type": "boolean", "scope": "resource", "description": "%config.suggestSmartCommit%", "default": true}, "git.enableCommitSigning": {"type": "boolean", "scope": "resource", "description": "%config.enableCommitSigning%", "default": false}, "git.confirmEmptyCommits": {"type": "boolean", "scope": "resource", "description": "%config.confirmEmptyCommits%", "default": true}, "git.decorations.enabled": {"type": "boolean", "default": true, "description": "%config.decorations.enabled%"}, "git.enableStatusBarSync": {"type": "boolean", "default": true, "description": "%config.enableStatusBarSync%", "scope": "resource"}, "git.followTagsWhenSync": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.followTagsWhenSync%"}, "git.replaceTagsWhenPull": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.replaceTagsWhenPull%"}, "git.promptToSaveFilesBeforeStash": {"type": "string", "enum": ["always", "staged", "never"], "enumDescriptions": ["%config.promptToSaveFilesBeforeStash.always%", "%config.promptToSaveFilesBeforeStash.staged%", "%config.promptToSaveFilesBeforeStash.never%"], "scope": "resource", "default": "always", "description": "%config.promptToSaveFilesBeforeStash%"}, "git.promptToSaveFilesBeforeCommit": {"type": "string", "enum": ["always", "staged", "never"], "enumDescriptions": ["%config.promptToSaveFilesBeforeCommit.always%", "%config.promptToSaveFilesBeforeCommit.staged%", "%config.promptToSaveFilesBeforeCommit.never%"], "scope": "resource", "default": "always", "description": "%config.promptToSaveFilesBeforeCommit%"}, "git.postCommitCommand": {"type": "string", "enum": ["none", "push", "sync"], "enumDescriptions": ["%config.postCommitCommand.none%", "%config.postCommitCommand.push%", "%config.postCommitCommand.sync%"], "markdownDescription": "%config.postCommitCommand%", "scope": "resource", "default": "none"}, "git.rememberPostCommitCommand": {"type": "boolean", "description": "%config.rememberPostCommitCommand%", "scope": "resource", "default": false}, "git.openAfterClone": {"type": "string", "enum": ["always", "alwaysNewWindow", "whenNoFolderOpen", "prompt"], "enumDescriptions": ["%config.openAfterClone.always%", "%config.openAfterClone.alwaysNewWindow%", "%config.openAfterClone.whenNoFolderOpen%", "%config.openAfterClone.prompt%"], "default": "prompt", "description": "%config.openAfterClone%"}, "git.showInlineOpenFileAction": {"type": "boolean", "default": true, "description": "%config.showInlineOpenFileAction%"}, "git.showPushSuccessNotification": {"type": "boolean", "description": "%config.showPushSuccessNotification%", "default": false}, "git.inputValidation": {"type": "boolean", "default": false, "description": "%config.inputValidation%"}, "git.inputValidationLength": {"type": "number", "default": 72, "description": "%config.inputValidationLength%"}, "git.inputValidationSubjectLength": {"type": ["number", "null"], "default": 50, "markdownDescription": "%config.inputValidationSubjectLength%"}, "git.detectSubmodules": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.detectSubmodules%"}, "git.detectSubmodulesLimit": {"type": "number", "scope": "resource", "default": 10, "description": "%config.detectSubmodulesLimit%"}, "git.alwaysShowStagedChangesResourceGroup": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.alwaysShowStagedChangesResourceGroup%"}, "git.alwaysSignOff": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.alwaysSignOff%"}, "git.ignoreSubmodules": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.ignoreSubmodules%"}, "git.ignoredRepositories": {"type": "array", "items": {"type": "string"}, "default": [], "scope": "window", "description": "%config.ignoredRepositories%"}, "git.scanRepositories": {"type": "array", "items": {"type": "string"}, "default": [], "scope": "resource", "description": "%config.scanRepositories%"}, "git.showProgress": {"type": "boolean", "description": "%config.showProgress%", "default": true, "scope": "resource"}, "git.rebaseWhenSync": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.rebaseWhenSync%"}, "git.pullBeforeCheckout": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.pullBeforeCheckout%"}, "git.fetchOnPull": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.fetchOnPull%"}, "git.pruneOnFetch": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.pruneOnFetch%"}, "git.pullTags": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.pullTags%"}, "git.autoStash": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.autoStash%"}, "git.allowForcePush": {"type": "boolean", "default": false, "description": "%config.allowForcePush%"}, "git.useForcePushWithLease": {"type": "boolean", "default": true, "description": "%config.useForcePushWithLease%"}, "git.useForcePushIfIncludes": {"type": "boolean", "default": true, "markdownDescription": "%config.useForcePushIfIncludes%"}, "git.confirmForcePush": {"type": "boolean", "default": true, "description": "%config.confirmForcePush%"}, "git.allowNoVerifyCommit": {"type": "boolean", "default": false, "description": "%config.allowNoVerifyCommit%"}, "git.confirmNoVerifyCommit": {"type": "boolean", "default": true, "description": "%config.confirmNoVerifyCommit%"}, "git.closeDiffOnOperation": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.closeDiffOnOperation%"}, "git.openDiffOnClick": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.openDiffOnClick%"}, "git.supportCancellation": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.supportCancellation%"}, "git.branchSortOrder": {"type": "string", "enum": ["committerdate", "alphabetically"], "default": "committerdate", "description": "%config.branchSortOrder%"}, "git.untrackedChanges": {"type": "string", "enum": ["mixed", "separate", "hidden"], "enumDescriptions": ["%config.untrackedChanges.mixed%", "%config.untrackedChanges.separate%", "%config.untrackedChanges.hidden%"], "default": "mixed", "description": "%config.untrackedChanges%", "scope": "resource"}, "git.requireGitUserConfig": {"type": "boolean", "description": "%config.requireGitUserConfig%", "default": true, "scope": "resource"}, "git.showCommitInput": {"type": "boolean", "scope": "resource", "default": true, "description": "%config.showCommitInput%"}, "git.terminalAuthentication": {"type": "boolean", "default": true, "description": "%config.terminalAuthentication%"}, "git.terminalGitEditor": {"type": "boolean", "default": false, "description": "%config.terminalGitEditor%"}, "git.useCommitInputAsStashMessage": {"type": "boolean", "scope": "resource", "default": false, "description": "%config.useCommitInputAsStashMessage%"}, "git.useIntegratedAskPass": {"type": "boolean", "default": true, "description": "%config.useIntegratedAskPass%"}, "git.githubAuthentication": {"markdownDeprecationMessage": "This setting is now deprecated, please use `#github.gitAuthentication#` instead."}, "git.timeline.date": {"type": "string", "enum": ["committed", "authored"], "enumDescriptions": ["%config.timeline.date.committed%", "%config.timeline.date.authored%"], "default": "committed", "description": "%config.timeline.date%", "scope": "window"}, "git.timeline.showAuthor": {"type": "boolean", "default": true, "description": "%config.timeline.showAuthor%", "scope": "window"}, "git.timeline.showUncommitted": {"type": "boolean", "default": false, "description": "%config.timeline.showUncommitted%", "scope": "window"}, "git.showActionButton": {"type": "object", "additionalProperties": false, "description": "%config.showActionButton%", "properties": {"commit": {"type": "boolean", "description": "%config.showActionButton.commit%"}, "publish": {"type": "boolean", "description": "%config.showActionButton.publish%"}, "sync": {"type": "boolean", "description": "%config.showActionButton.sync%"}}, "default": {"commit": true, "publish": true, "sync": true}, "scope": "resource"}, "git.statusLimit": {"type": "number", "scope": "resource", "default": 10000, "description": "%config.statusLimit%"}, "git.repositoryScanIgnoredFolders": {"type": "array", "items": {"type": "string"}, "default": ["node_modules"], "scope": "resource", "markdownDescription": "%config.repositoryScanIgnoredFolders%"}, "git.repositoryScanMaxDepth": {"type": "number", "scope": "resource", "default": 1, "markdownDescription": "%config.repositoryScanMaxDepth%"}, "git.commandsToLog": {"type": "array", "items": {"type": "string"}, "default": [], "markdownDescription": "%config.commandsToLog%"}, "git.mergeEditor": {"type": "boolean", "default": false, "markdownDescription": "%config.mergeEditor%", "scope": "window"}, "git.optimisticUpdate": {"type": "boolean", "default": true, "markdownDescription": "%config.optimisticUpdate%", "scope": "resource", "tags": ["experimental"]}, "git.openRepositoryInParentFolders": {"type": "string", "enum": ["always", "never", "prompt"], "enumDescriptions": ["%config.openRepositoryInParentFolders.always%", "%config.openRepositoryInParentFolders.never%", "%config.openRepositoryInParentFolders.prompt%"], "default": "prompt", "markdownDescription": "%config.openRepositoryInParentFolders%", "scope": "resource"}, "git.similarityThreshold": {"type": "number", "default": 50, "minimum": 0, "maximum": 100, "markdownDescription": "%config.similarityThreshold%", "scope": "resource"}, "git.blame.editorDecoration.enabled": {"type": "boolean", "default": false, "markdownDescription": "%config.blameEditorDecoration.enabled%"}, "git.blame.editorDecoration.template": {"type": "string", "default": "${subject}, ${authorName} (${authorDateAgo})", "markdownDescription": "%config.blameEditorDecoration.template%"}, "git.blame.statusBarItem.enabled": {"type": "boolean", "default": true, "markdownDescription": "%config.blameStatusBarItem.enabled%"}, "git.blame.statusBarItem.template": {"type": "string", "default": "${authorName} (${authorDateAgo})", "markdownDescription": "%config.blameStatusBarItem.template%"}, "git.commitShortHashLength": {"type": "number", "default": 7, "minimum": 7, "maximum": 40, "markdownDescription": "%config.commitShortHashLength%", "scope": "resource"}, "git.diagnosticsCommitHook.Enabled": {"type": "boolean", "default": false, "markdownDescription": "%config.diagnosticsCommitHook.Enabled%", "scope": "resource"}, "git.diagnosticsCommitHook.Sources": {"type": "object", "additionalProperties": {"type": "string", "enum": ["error", "warning", "information", "hint", "none"]}, "default": {"*": "error"}, "markdownDescription": "%config.diagnosticsCommitHook.Sources%", "scope": "resource"}, "git.discardUntrackedChangesToTrash": {"type": "boolean", "default": true, "markdownDescription": "%config.discardUntrackedChangesToTrash%"}, "git.showReferenceDetails": {"type": "boolean", "default": true, "markdownDescription": "%config.showReferenceDetails%"}}}, "colors": [{"id": "gitDecoration.addedResourceForeground", "description": "%colors.added%", "defaults": {"light": "#587c0c", "dark": "#81b88b", "highContrast": "#a1e3ad", "highContrastLight": "#374e06"}}, {"id": "gitDecoration.modifiedResourceForeground", "description": "%colors.modified%", "defaults": {"light": "#895503", "dark": "#E2C08D", "highContrast": "#E2C08D", "highContrastLight": "#895503"}}, {"id": "gitDecoration.deletedResourceForeground", "description": "%colors.deleted%", "defaults": {"light": "#ad0707", "dark": "#c74e39", "highContrast": "#c74e39", "highContrastLight": "#ad0707"}}, {"id": "gitDecoration.renamedResourceForeground", "description": "%colors.renamed%", "defaults": {"light": "#007100", "dark": "#73C991", "highContrast": "#73C991", "highContrastLight": "#007100"}}, {"id": "gitDecoration.untrackedResourceForeground", "description": "%colors.untracked%", "defaults": {"light": "#007100", "dark": "#73C991", "highContrast": "#73C991", "highContrastLight": "#007100"}}, {"id": "gitDecoration.ignoredResourceForeground", "description": "%colors.ignored%", "defaults": {"light": "#8E8E90", "dark": "#8C8C8C", "highContrast": "#A7A8A9", "highContrastLight": "#8e8e90"}}, {"id": "gitDecoration.stageModifiedResourceForeground", "description": "%colors.stageModified%", "defaults": {"light": "#895503", "dark": "#E2C08D", "highContrast": "#E2C08D", "highContrastLight": "#895503"}}, {"id": "gitDecoration.stageDeletedResourceForeground", "description": "%colors.stageDeleted%", "defaults": {"light": "#ad0707", "dark": "#c74e39", "highContrast": "#c74e39", "highContrastLight": "#ad0707"}}, {"id": "gitDecoration.conflictingResourceForeground", "description": "%colors.conflict%", "defaults": {"light": "#ad0707", "dark": "#e4676b", "highContrast": "#c74e39", "highContrastLight": "#ad0707"}}, {"id": "gitDecoration.submoduleResourceForeground", "description": "%colors.submodule%", "defaults": {"light": "#1258a7", "dark": "#8db9e2", "highContrast": "#8db9e2", "highContrastLight": "#1258a7"}}, {"id": "git.blame.editorDecorationForeground", "description": "%colors.blameEditorDecoration%", "defaults": {"dark": "editorInlayHint.foreground", "light": "editorInlayHint.foreground", "highContrast": "editorInlayHint.foreground", "highContrastLight": "editorInlayHint.foreground"}}], "configurationDefaults": {"[git-commit]": {"editor.rulers": [50, 72], "editor.wordWrap": "off", "workbench.editor.restoreViewState": false}, "[git-rebase]": {"workbench.editor.restoreViewState": false}}, "viewsWelcome": [{"view": "scm", "contents": "%view.workbench.scm.disabled%", "when": "!config.git.enabled"}, {"view": "scm", "contents": "%view.workbench.scm.missing%", "when": "config.git.enabled && git.missing && remoteName != ''"}, {"view": "scm", "contents": "%view.workbench.scm.missing.mac%", "when": "config.git.enabled && git.missing && remoteName == '' && isMac"}, {"view": "scm", "contents": "%view.workbench.scm.missing.windows%", "when": "config.git.enabled && git.missing && remoteName == '' && isWindows"}, {"view": "scm", "contents": "%view.workbench.scm.missing.linux%", "when": "config.git.enabled && git.missing && remoteName == '' && isLinux"}, {"view": "scm", "contents": "%view.workbench.scm.empty%", "when": "config.git.enabled && !git.missing && workbenchState == empty && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0", "enablement": "git.state == initialized", "group": "2_open@1"}, {"view": "scm", "contents": "%view.workbench.scm.emptyWorkspace%", "when": "config.git.enabled && !git.missing && workbenchState == workspace && workspaceFolderCount == 0 && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0", "enablement": "git.state == initialized", "group": "2_open@1"}, {"view": "scm", "contents": "%view.workbench.scm.scanFolderForRepositories%", "when": "config.git.enabled && !git.missing && workbenchState == folder && workspaceFolderCount != 0 && git.state != initialized"}, {"view": "scm", "contents": "%view.workbench.scm.scanWorkspaceForRepositories%", "when": "config.git.enabled && !git.missing && workbenchState == workspace && workspaceFolderCount != 0 && git.state != initialized"}, {"view": "scm", "contents": "%view.workbench.scm.folder%", "when": "config.git.enabled && !git.missing && git.state == initialized && workbenchState == folder && scm.providerCount == 0 && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0 && remoteName != 'codespaces'", "group": "5_scm@1"}, {"view": "scm", "contents": "%view.workbench.scm.workspace%", "when": "config.git.enabled && !git.missing && git.state == initialized && workbenchState == workspace && workspaceFolderCount != 0 && scm.providerCount == 0 && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0 && remoteName != 'codespaces'", "group": "5_scm@1"}, {"view": "scm", "contents": "%view.workbench.scm.repositoryInParentFolders%", "when": "config.git.enabled && !git.missing && git.state == initialized && git.parentRepositoryCount == 1"}, {"view": "scm", "contents": "%view.workbench.scm.repositoriesInParentFolders%", "when": "config.git.enabled && !git.missing && git.state == initialized && git.parentRepositoryCount > 1"}, {"view": "scm", "contents": "%view.workbench.scm.unsafeRepository%", "when": "config.git.enabled && !git.missing && git.state == initialized && git.unsafeRepositoryCount == 1"}, {"view": "scm", "contents": "%view.workbench.scm.unsafeRepositories%", "when": "config.git.enabled && !git.missing && git.state == initialized && git.unsafeRepositoryCount > 1"}, {"view": "scm", "contents": "%view.workbench.scm.closedRepository%", "when": "config.git.enabled && !git.missing && git.state == initialized && git.closedRepositoryCount == 1"}, {"view": "scm", "contents": "%view.workbench.scm.closedRepositories%", "when": "config.git.enabled && !git.missing && git.state == initialized && git.closedRepositoryCount > 1"}, {"view": "explorer", "contents": "%view.workbench.cloneRepository%", "when": "config.git.enabled && git.state == initialized && scm.providerCount == 0", "group": "5_scm@1"}, {"view": "explorer", "contents": "%view.workbench.learnMore%", "when": "config.git.enabled && git.state == initialized && scm.providerCount == 0", "group": "5_scm@10"}]}, "dependencies": {"@joaomoreno/unique-names-generator": "^5.2.0", "@vscode/extension-telemetry": "^0.9.8", "byline": "^5.0.0", "file-type": "16.5.4", "picomatch": "2.3.1", "vscode-uri": "^2.0.0", "which": "4.0.0"}, "devDependencies": {"@types/byline": "4.2.31", "@types/mocha": "^9.1.1", "@types/node": "20.x", "@types/picomatch": "2.3.0", "@types/which": "3.0.0"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}