# cleanup rules for web node modules, .gitignore style

**/*.txt
**/*.json
**/*.md
**/*.d.ts
**/*.js.map
**/LICENSE
**/CONTRIBUTORS

**/docs/**
**/example/**
**/examples/**

jschardet/index.js
jschardet/src/**
jschardet/dist/jschardet.js

vscode-textmate/webpack.config.js

@xterm/xterm/src/**

@xterm/addon-clipboard/src/**
@xterm/addon-clipboard/out/**

@xterm/addon-image/src/**
@xterm/addon-image/out/**

@xterm/addon-ligatures/src/**
@xterm/addon-ligatures/out/**

@xterm/addon-search/src/**
@xterm/addon-search/out/**
@xterm/addon-search/fixtures/**

@xterm/addon-unicode11/src/**
@xterm/addon-unicode11/out/**

@xterm/addon-webgl/src/**
@xterm/addon-webgl/out/**

# This makes sure the model is included in the package
!@vscode/vscode-languagedetection/model/**
!@vscode/tree-sitter-wasm/wasm/**

# Ensure only the required telemetry pieces are loaded in web to reduce bundle size
@microsoft/1ds-core-js/**
@microsoft/1ds-post-js/**
@microsoft/applicationinsights-core-js/**
@microsoft/applicationinsights-shims/**
!@microsoft/1ds-core-js/dist/ms.core.min.js
!@microsoft/1ds-core-js/bundle/ms.core.min.js
!@microsoft/1ds-post-js/dist/ms.post.min.js
!@microsoft/1ds-post-js/bundle/ms.post.min.js
!@microsoft/applicationinsights-core-js/browser/applicationinsights-core-js.min.js
!@microsoft/applicationinsights-shims/dist/umd/applicationinsights-shims.min.js

vsda/**
!vsda/rust/web/**
